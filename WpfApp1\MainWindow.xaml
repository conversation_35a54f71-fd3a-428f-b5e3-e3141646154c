﻿<Window x:Class="WpfApp1.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp1"
        mc:Ignorable="d"
        Title="نظام إدارة المهام" Height="700" Width="1200" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات -->
        <ToolBar Grid.Row="0" Background="LightBlue">
            <Button Name="btnAddTask" Content="إضافة مهمة" Click="BtnAddTask_Click" Margin="5"/>
            <Button Name="btnRefresh" Content="تحديث" Click="BtnRefresh_Click" Margin="5"/>
            <Separator/>
            <TextBox Name="txtSearch" Width="200" Margin="5" Text="البحث في المهام..."/>
            <Button Name="btnSearch" Content="بحث" Click="BtnSearch_Click" Margin="5"/>
            <Separator/>
            <ComboBox Name="cmbViewType" Width="120" Margin="5" SelectionChanged="CmbViewType_SelectionChanged">
                <ComboBoxItem Content="عرض يومي" Tag="Day"/>
                <ComboBoxItem Content="عرض أسبوعي" Tag="Week"/>
                <ComboBoxItem Content="عرض شهري" Tag="Month" IsSelected="True"/>
            </ComboBox>
        </ToolBar>

        <!-- منطقة عرض المهام -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة المهام -->
            <DataGrid Grid.Column="0" Name="dgTasks" Margin="0,0,10,0"
                      AutoGenerateColumns="False" CanUserAddRows="False"
                      SelectionMode="Single" GridLinesVisibility="Horizontal">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="200"/>
                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="250"/>
                    <DataGridTextColumn Header="تاريخ البداية" Binding="{Binding StartDate, StringFormat=dd/MM/yyyy HH:mm}" Width="150"/>
                    <DataGridTextColumn Header="تاريخ النهاية" Binding="{Binding EndDate, StringFormat=dd/MM/yyyy HH:mm}" Width="150"/>
                    <DataGridTextColumn Header="الأولوية" Binding="{Binding Priority}" Width="80"/>
                    <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="80"/>
                    <DataGridCheckBoxColumn Header="مكتملة" Binding="{Binding IsCompleted}" Width="60"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- لوحة التحكم -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <GroupBox Header="تفاصيل المهمة" Margin="0,0,0,10">
                    <StackPanel Margin="10">
                        <TextBlock Name="txtSelectedTaskTitle" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock Name="txtSelectedTaskDescription" TextWrapping="Wrap" Margin="0,0,0,10"/>
                        <TextBlock Name="txtSelectedTaskDates" Margin="0,0,0,5"/>
                        <TextBlock Name="txtSelectedTaskInfo" Margin="0,0,0,10"/>
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="إجراءات">
                    <StackPanel Margin="10">
                        <Button Name="btnEditTask" Content="تعديل المهمة" Margin="0,0,0,5" Click="BtnEditTask_Click"/>
                        <Button Name="btnDeleteTask" Content="حذف المهمة" Margin="0,0,0,5" Click="BtnDeleteTask_Click"/>
                        <Button Name="btnToggleComplete" Content="تغيير الحالة" Margin="0,0,0,5" Click="BtnToggleComplete_Click"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </Grid>

        <!-- شريط الحالة -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Name="txtStatus" Text="جاهز"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="txtTaskCount" Text="عدد المهام: 0"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
