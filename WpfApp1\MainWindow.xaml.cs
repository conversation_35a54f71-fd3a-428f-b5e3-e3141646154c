﻿using System.Collections.ObjectModel;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using WpfApp1.Models;
using WpfApp1.Services;
using WpfApp1.Views;

namespace WpfApp1
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly TaskService _taskService;
        private ObservableCollection<Models.Task> _tasks;

        public MainWindow()
        {
            InitializeComponent();
            _taskService = new TaskService();
            _tasks = new ObservableCollection<Models.Task>();

            InitializeDataGrid();
            LoadTasks();
        }

        private void InitializeDataGrid()
        {
            // ربط البيانات بالجدول
            dgTasks.ItemsSource = _tasks;

            // أحداث الجدول
            dgTasks.SelectionChanged += DgTasks_SelectionChanged;
        }

        private async void LoadTasks()
        {
            try
            {
                if (txtStatus != null)
                    txtStatus.Text = "جاري تحميل المهام...";

                var tasks = await _taskService.GetAllTasksAsync();

                _tasks.Clear();
                foreach (var task in tasks)
                {
                    _tasks.Add(task);
                }

                UpdateTaskCount();

                if (txtStatus != null)
                    txtStatus.Text = "تم تحميل المهام بنجاح";
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل المهام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                if (txtStatus != null)
                    txtStatus.Text = "فشل في تحميل المهام";
            }
        }

        private void UpdateTaskCount()
        {
            if (txtTaskCount != null)
                txtTaskCount.Text = $"عدد المهام: {_tasks.Count}";
        }

        private async void BtnAddTask_Click(object sender, RoutedEventArgs e)
        {
            var addTaskWindow = new AddTaskWindow();
            if (addTaskWindow.ShowDialog() == true)
            {
                try
                {
                    var newTask = await _taskService.AddTaskAsync(addTaskWindow.Task);
                    _tasks.Add(newTask);
                    UpdateTaskCount();
                    if (txtStatus != null)
                        txtStatus.Text = "تم إضافة المهمة بنجاح";
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في إضافة المهمة: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await System.Threading.Tasks.Task.Run(() => LoadTasks());
        }

        private async void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text) || txtSearch.Text == "البحث في المهام...")
            {
                LoadTasks();
                return;
            }

            try
            {
                if (txtStatus != null)
                    txtStatus.Text = "جاري البحث...";

                var searchResults = await _taskService.SearchTasksAsync(txtSearch.Text);

                _tasks.Clear();
                foreach (var task in searchResults)
                {
                    _tasks.Add(task);
                }

                UpdateTaskCount();

                if (txtStatus != null)
                    txtStatus.Text = $"تم العثور على {searchResults.Count} مهمة";
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbViewType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // التحقق من أن العناصر تم تهيئتها
                if (txtStatus == null) return;

                var selectedItem = cmbViewType.SelectedItem as ComboBoxItem;
                if (selectedItem?.Tag != null)
                {
                    txtStatus.Text = $"تم تغيير العرض إلى: {selectedItem.Content}";
                }
            }
            catch (Exception ex)
            {
                if (txtStatus != null)
                {
                    txtStatus.Text = $"خطأ في تغيير العرض: {ex.Message}";
                }
            }
        }

        private void DgTasks_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedTask = dgTasks.SelectedItem as Models.Task;
            if (selectedTask != null)
            {
                UpdateTaskDetails(selectedTask);
            }
            else
            {
                ClearTaskDetails();
            }
        }

        private void UpdateTaskDetails(Models.Task task)
        {
            txtSelectedTaskTitle.Text = $"العنوان: {task.Title}";
            txtSelectedTaskDescription.Text = $"الوصف: {task.Description ?? "لا يوجد وصف"}";

            var startDate = DateTimeOffset.FromUnixTimeSeconds(task.StartDate ?? 0).DateTime;
            var dueDate = DateTimeOffset.FromUnixTimeSeconds(task.DueDate ?? 0).DateTime;
            txtSelectedTaskDates.Text = $"من: {startDate:dd/MM/yyyy HH:mm} إلى: {dueDate:dd/MM/yyyy HH:mm}";

            var isCompleted = task.Status == "Completed";
            txtSelectedTaskInfo.Text = $"الأولوية: {task.Priority} | الحالة: {(isCompleted ? "مكتملة" : "معلقة")}";
        }

        private void ClearTaskDetails()
        {
            txtSelectedTaskTitle.Text = "";
            txtSelectedTaskDescription.Text = "";
            txtSelectedTaskDates.Text = "";
            txtSelectedTaskInfo.Text = "";
        }

        private async void BtnEditTask_Click(object sender, RoutedEventArgs e)
        {
            var selectedTask = dgTasks.SelectedItem as Models.Task;
            if (selectedTask == null)
            {
                System.Windows.MessageBox.Show("يرجى اختيار مهمة للتعديل", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var editWindow = new AddTaskWindow(selectedTask);
            if (editWindow.ShowDialog() == true)
            {
                try
                {
                    await _taskService.UpdateTaskAsync(editWindow.Task);
                    LoadTasks(); // إعادة تحميل البيانات
                    if (txtStatus != null)
                        txtStatus.Text = "تم تحديث المهمة بنجاح";
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في تحديث المهمة: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BtnDeleteTask_Click(object sender, RoutedEventArgs e)
        {
            var selectedTask = dgTasks.SelectedItem as Models.Task;
            if (selectedTask == null)
            {
                System.Windows.MessageBox.Show("يرجى اختيار مهمة للحذف", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = System.Windows.MessageBox.Show($"هل أنت متأكد من حذف المهمة '{selectedTask.Title}'؟",
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _taskService.DeleteTaskAsync(selectedTask.Id);
                    _tasks.Remove(selectedTask);
                    UpdateTaskCount();
                    ClearTaskDetails();
                    if (txtStatus != null)
                        txtStatus.Text = "تم حذف المهمة بنجاح";
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في حذف المهمة: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BtnToggleComplete_Click(object sender, RoutedEventArgs e)
        {
            var selectedTask = dgTasks.SelectedItem as Models.Task;
            if (selectedTask == null)
            {
                System.Windows.MessageBox.Show("يرجى اختيار مهمة لتغيير حالتها", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                bool currentStatus = selectedTask.Status == "Completed";
                bool newStatus = !currentStatus;
                await _taskService.UpdateTaskStatusAsync(selectedTask.Id, newStatus);
                selectedTask.Status = newStatus ? "Completed" : "Pending";

                dgTasks.Items.Refresh();
                UpdateTaskDetails(selectedTask);
                if (txtStatus != null)
                    txtStatus.Text = $"تم تغيير حالة المهمة إلى: {(newStatus ? "مكتملة" : "معلقة")}";
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تغيير حالة المهمة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _taskService?.Dispose();
            base.OnClosed(e);
        }
    }
}