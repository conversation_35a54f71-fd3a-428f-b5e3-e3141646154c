﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Action
{
    public int Id { get; set; }

    public string ActionName { get; set; } = null!;

    public string ActionCode { get; set; } = null!;

    public string? Description { get; set; }

    public bool IsActive { get; set; }

    public long CreatedAt { get; set; }

    public virtual ICollection<Permission> Permissions { get; set; } = new List<Permission>();

    public virtual ICollection<ScreenAction> ScreenActions { get; set; } = new List<ScreenAction>();
}
