﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

/// <summary>
/// جدول سجلات الأنشطة - يحتوي على جميع الأنشطة والتغييرات في النظام
/// </summary>
public partial class ActivityLog
{
    /// <summary>
    /// المعرف الفريد لسجل النشاط
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// معرف المهمة المرتبطة بالنشاط (0 للأنشطة العامة)
    /// </summary>
    public int TaskId { get; set; }

    /// <summary>
    /// معرف المستخدم الذي قام بالنشاط
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// نوع الإجراء المنفذ (created, updated, deleted, etc.)
    /// </summary>
    public string Action { get; set; } = null!;

    /// <summary>
    /// تفاصيل إضافية عن النشاط
    /// </summary>
    public string? Details { get; set; }

    /// <summary>
    /// الطابع الزمني للنشاط (Unix timestamp)
    /// </summary>
    public long Timestamp { get; set; }

    public string? ChangeType { get; set; }

    public string? ChangeDescription { get; set; }

    public string? OldValue { get; set; }

    public string? NewValue { get; set; }

    public int? ChangedBy { get; set; }

    public long? ChangedAt { get; set; }

    public virtual User? ChangedByNavigation { get; set; }

    public virtual Task Task { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
