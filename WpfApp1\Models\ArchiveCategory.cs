﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class ArchiveCategory
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public int? ParentId { get; set; }

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public string? Color { get; set; }

    public string? Icon { get; set; }

    public bool IsActive { get; set; }

    public virtual ICollection<ArchiveDocument> ArchiveDocuments { get; set; } = new List<ArchiveDocument>();

    public virtual User CreatedByNavigation { get; set; } = null!;

    public virtual ICollection<ArchiveCategory> InverseParent { get; set; } = new List<ArchiveCategory>();

    public virtual ArchiveCategory? Parent { get; set; }
}
