﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Attachment
{
    public int Id { get; set; }

    public int TaskId { get; set; }

    public string FileName { get; set; } = null!;

    public string FilePath { get; set; } = null!;

    public long FileSize { get; set; }

    public string FileType { get; set; } = null!;

    public int UploadedBy { get; set; }

    public long UploadedAt { get; set; }

    public bool IsDeleted { get; set; }

    public string? UniqueFileName { get; set; }

    public string StorageFolder { get; set; } = null!;

    public string? FileHash { get; set; }

    public long? CompressedSize { get; set; }

    public bool IsCompressed { get; set; }

    public string? Metadata { get; set; }

    public long? LastAccessedAt { get; set; }

    public int DownloadCount { get; set; }

    public bool IsBackedUp { get; set; }

    public long? LastBackupAt { get; set; }

    public virtual Task Task { get; set; } = null!;

    public virtual User UploadedByNavigation { get; set; } = null!;
}
