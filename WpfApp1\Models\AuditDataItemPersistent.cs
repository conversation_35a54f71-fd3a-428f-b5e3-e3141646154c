﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class AuditDataItemPersistent
{
    public Guid Oid { get; set; }

    public int? Gcrecord { get; set; }

    public int? OptimisticLockField { get; set; }

    public string? ObjectType { get; set; }

    public string? ObjectKey { get; set; }

    public string? PropertyName { get; set; }

    public string? OldValue { get; set; }

    public string? NewValue { get; set; }

    public DateTime? ModifiedOn { get; set; }

    public string? UserName { get; set; }

    public int? OperationType { get; set; }

    public Guid? TransactionId { get; set; }
}
