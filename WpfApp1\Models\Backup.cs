﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Backup
{
    public int Id { get; set; }

    public string FileName { get; set; } = null!;

    public string FilePath { get; set; } = null!;

    public long FileSize { get; set; }

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public string? Description { get; set; }

    public bool IsAutoBackup { get; set; }

    public bool IsRestored { get; set; }

    public long? RestoredAt { get; set; }

    public int? RestoredBy { get; set; }

    public virtual User CreatedByNavigation { get; set; } = null!;

    public virtual User? RestoredByNavigation { get; set; }
}
