﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class CalendarEvent
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public long StartTime { get; set; }

    public long EndTime { get; set; }

    public bool AllDay { get; set; }

    public string? Location { get; set; }

    public string? Color { get; set; }

    public int UserId { get; set; }

    public int? TaskId { get; set; }

    public string? RecurrenceRule { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public long? Duration { get; set; }

    public bool ReminderEnabled { get; set; }

    public int? ReminderMinutes { get; set; }

    public bool ReminderSent { get; set; }

    public string EventType { get; set; } = null!;

    public string RecurrencePattern { get; set; } = null!;

    public int RecurrenceCount { get; set; }

    public long? RecurrenceEndDate { get; set; }

    public virtual Task? Task { get; set; }

    public virtual User User { get; set; } = null!;
}
