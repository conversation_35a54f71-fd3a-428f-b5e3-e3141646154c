﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class ChatGroup
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool? IsDirectMessage { get; set; }

    public int? CreatedBy { get; set; }

    public long? CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool? IsDeleted { get; set; }

    public string? GroupType { get; set; }

    public bool? IsPrivate { get; set; }

    public int? MaxMembers { get; set; }

    public bool IsArchived { get; set; }

    public string? ImageUrl { get; set; }

    public virtual User? CreatedByNavigation { get; set; }

    public virtual ICollection<GroupMember> GroupMembers { get; set; } = new List<GroupMember>();

    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
}
