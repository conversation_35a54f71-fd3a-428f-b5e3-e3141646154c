﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class CustomRole
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public int Level { get; set; }

    public string? Color { get; set; }

    public string? Icon { get; set; }

    public bool IsActive { get; set; }

    public bool IsDeleted { get; set; }

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public int? UpdatedBy { get; set; }

    public virtual User CreatedByNavigation { get; set; } = null!;

    public virtual ICollection<CustomRolePermission> CustomRolePermissions { get; set; } = new List<CustomRolePermission>();

    public virtual User? UpdatedByNavigation { get; set; }

    public virtual ICollection<UserCustomRole> UserCustomRoles { get; set; } = new List<UserCustomRole>();
}
