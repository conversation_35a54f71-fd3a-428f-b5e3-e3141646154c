﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class CustomRolePermission
{
    public int Id { get; set; }

    public int CustomRoleId { get; set; }

    public int PermissionId { get; set; }

    public bool IsActive { get; set; }

    public int GrantedBy { get; set; }

    public long GrantedAt { get; set; }

    public long? ExpiresAt { get; set; }

    public bool IsDeleted { get; set; }

    public virtual CustomRole CustomRole { get; set; } = null!;

    public virtual User GrantedByNavigation { get; set; } = null!;

    public virtual Permission Permission { get; set; } = null!;
}
