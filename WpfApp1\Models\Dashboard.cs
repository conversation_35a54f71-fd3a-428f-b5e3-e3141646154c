﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Dashboard
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public int OwnerId { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDefault { get; set; }

    public bool IsShared { get; set; }

    public int GridRows { get; set; }

    public int GridColumns { get; set; }

    public string? Settings { get; set; }

    public string? Color { get; set; }

    public string? Icon { get; set; }

    public bool IsDeleted { get; set; }

    public virtual ICollection<DashboardWidget> DashboardWidgets { get; set; } = new List<DashboardWidget>();

    public virtual User Owner { get; set; } = null!;

    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
