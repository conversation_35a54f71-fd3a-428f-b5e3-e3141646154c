﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Department
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public int? ManagerId { get; set; }

    public bool IsActive { get; set; }

    public long CreatedAt { get; set; }

    public int? ParentId { get; set; }

    public int Level { get; set; }

    public int SortOrder { get; set; }

    public virtual ICollection<Department> InverseParent { get; set; } = new List<Department>();

    public virtual User? Manager { get; set; }

    public virtual Department? Parent { get; set; }

    public virtual ICollection<Task> Tasks { get; set; } = new List<Task>();

    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
