﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Event
{
    public Guid Oid { get; set; }

    public int? Gcrecord { get; set; }

    public int? OptimisticLockField { get; set; }

    public string? ObjectType { get; set; }

    public string? Subject { get; set; }

    public string? Description { get; set; }

    public DateTime? StartOn { get; set; }

    public DateTime? EndOn { get; set; }

    public bool? AllDay { get; set; }

    public string? Location { get; set; }

    public int? Label { get; set; }

    public int? Status { get; set; }

    public string? RecurrenceInfo { get; set; }

    public string? ReminderInfo { get; set; }

    public string? ResourceIds { get; set; }

    public int? Type { get; set; }

    public DateTime? CreatedOn { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? LastModifiedOn { get; set; }

    public string? LastModifiedBy { get; set; }

    public int Id { get; set; }

    public DateTime? AlarmTime { get; set; }

    public bool? IsPostponed { get; set; }

    public string? RecurrenceInfoXml { get; set; }

    public int? RecurrencePatternId { get; set; }

    public int? RemindIn { get; set; }

    public string? ReminderInfoXml { get; set; }

    public virtual ICollection<EventResource> EventResources { get; set; } = new List<EventResource>();

    public virtual ICollection<Reminder> Reminders { get; set; } = new List<Reminder>();
}
