﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class EventResource
{
    public Guid Oid { get; set; }

    public int? Gcrecord { get; set; }

    public int? OptimisticLockField { get; set; }

    public Guid? Event { get; set; }

    public Guid? Resource { get; set; }

    public virtual Event? EventNavigation { get; set; }

    public virtual Resource? ResourceNavigation { get; set; }
}
