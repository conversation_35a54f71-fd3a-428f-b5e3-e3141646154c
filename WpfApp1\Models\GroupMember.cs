﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class GroupMember
{
    public int Id { get; set; }

    public int GroupId { get; set; }

    public int UserId { get; set; }

    public int Role { get; set; }

    public long JoinedAt { get; set; }

    public bool IsDeleted { get; set; }

    public long? LeftAt { get; set; }

    public virtual ChatGroup Group { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
