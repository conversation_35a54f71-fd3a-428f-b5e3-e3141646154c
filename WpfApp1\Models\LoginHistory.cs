﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class LoginHistory
{
    public int Id { get; set; }

    public int UserId { get; set; }

    public long LoginTime { get; set; }

    public long? LogoutTime { get; set; }

    public string? IpAddress { get; set; }

    public string? UserAgent { get; set; }

    public bool IsSuccessful { get; set; }

    public string? FailureReason { get; set; }

    public int? SessionDuration { get; set; }

    public virtual User User { get; set; } = null!;
}
