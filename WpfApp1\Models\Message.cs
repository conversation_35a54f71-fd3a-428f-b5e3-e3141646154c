﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Message
{
    public int Id { get; set; }

    public int GroupId { get; set; }

    public int SenderId { get; set; }

    public string Content { get; set; } = null!;

    public int ContentType { get; set; }

    public int? ReplyToMessageId { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public bool IsRead { get; set; }

    public bool IsPinned { get; set; }

    public long? PinnedAt { get; set; }

    public int? PinnedBy { get; set; }

    public int Priority { get; set; }

    public bool IsMarkedForFollowUp { get; set; }

    public long? FollowUpAt { get; set; }

    public int? MarkedForFollowUpBy { get; set; }

    public bool IsEdited { get; set; }

    public int? ReceiverId { get; set; }

    public long? SentAt { get; set; }

    public virtual ChatGroup Group { get; set; } = null!;

    public virtual ICollection<Message> InverseReplyToMessage { get; set; } = new List<Message>();

    public virtual User? MarkedForFollowUpByNavigation { get; set; }

    public virtual ICollection<MessageAttachment> MessageAttachments { get; set; } = new List<MessageAttachment>();

    public virtual ICollection<MessageReaction> MessageReactions { get; set; } = new List<MessageReaction>();

    public virtual ICollection<MessageRead> MessageReads { get; set; } = new List<MessageRead>();

    public virtual User? PinnedByNavigation { get; set; }

    public virtual User? Receiver { get; set; }

    public virtual Message? ReplyToMessage { get; set; }

    public virtual User Sender { get; set; } = null!;

    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
