﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class MessageAttachment
{
    public int Id { get; set; }

    public int MessageId { get; set; }

    public string FileName { get; set; } = null!;

    public string FilePath { get; set; } = null!;

    public long FileSize { get; set; }

    public string FileType { get; set; } = null!;

    public long UploadedAt { get; set; }

    public bool IsDeleted { get; set; }

    public int? UploadedBy { get; set; }

    public virtual Message Message { get; set; } = null!;

    public virtual User? UploadedByNavigation { get; set; }
}
