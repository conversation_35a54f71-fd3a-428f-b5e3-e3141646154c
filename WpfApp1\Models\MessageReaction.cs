﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class MessageReaction
{
    public int Id { get; set; }

    public int MessageId { get; set; }

    public int UserId { get; set; }

    public string Reaction { get; set; } = null!;

    public long CreatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public string? ReactionType { get; set; }

    public virtual Message Message { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
