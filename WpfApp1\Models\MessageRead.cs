﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

/// <summary>
/// جدول قراءة الرسائل العامة - يتتبع حالة قراءة كل رسالة لكل مستخدم في المحادثات العامة
/// </summary>
public partial class MessageRead
{
    /// <summary>
    /// معرف فريد لحالة القراءة
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// معرف الرسالة المقروءة
    /// </summary>
    public int MessageId { get; set; }

    /// <summary>
    /// معرف المستخدم الذي قرأ الرسالة
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// تاريخ قراءة الرسالة (Unix timestamp)
    /// </summary>
    public long ReadAt { get; set; }

    public virtual Message Message { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
