﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class NotificationSetting
{
    public int Id { get; set; }

    public int UserId { get; set; }

    public string NotificationType { get; set; } = null!;

    public bool IsEnabled { get; set; }

    public string DeliveryMethod { get; set; } = null!;

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public bool IsEmailEnabled { get; set; }

    public bool IsPushEnabled { get; set; }

    public bool IsSmsEnabled { get; set; }

    public virtual User User { get; set; } = null!;
}
