﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class PasswordResetToken
{
    public int Id { get; set; }

    public int UserId { get; set; }

    public string Token { get; set; } = null!;

    public long ExpiresAt { get; set; }

    public long CreatedAt { get; set; }

    public bool? IsUsed { get; set; }

    public long? UsedAt { get; set; }

    public virtual User User { get; set; } = null!;
}
