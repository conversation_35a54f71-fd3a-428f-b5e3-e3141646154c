﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class PermissionsBackupFinal
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string PermissionGroup { get; set; } = null!;

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public string? Category { get; set; }

    public int Level { get; set; }

    public string? Icon { get; set; }

    public string? Color { get; set; }

    public bool IsDefault { get; set; }

    public bool IsActive { get; set; }

    public int? ScreenId { get; set; }

    public int? ActionId { get; set; }
}
