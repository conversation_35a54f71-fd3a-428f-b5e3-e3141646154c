﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class RefreshToken
{
    public int Id { get; set; }

    public int UserId { get; set; }

    public string Token { get; set; } = null!;

    public long ExpiresAt { get; set; }

    public long CreatedAt { get; set; }

    public bool? IsRevoked { get; set; }

    public long? RevokedAt { get; set; }

    public string? ReplacedByToken { get; set; }

    public virtual User User { get; set; } = null!;
}
