﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Reminder
{
    public Guid Oid { get; set; }

    public int? Gcrecord { get; set; }

    public int? OptimisticLockField { get; set; }

    public Guid? Event { get; set; }

    public DateTime? AlarmTime { get; set; }

    public bool? IsPostponed { get; set; }

    public int? RemindIn { get; set; }

    public string? ReminderInfoXml { get; set; }

    public virtual Event? EventNavigation { get; set; }
}
