﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Report
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public string ReportType { get; set; } = null!;

    public string Query { get; set; } = null!;

    public string? Parameters { get; set; }

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsPublic { get; set; }

    public bool IsDeleted { get; set; }

    public virtual User CreatedByNavigation { get; set; } = null!;

    public virtual ICollection<ReportSchedule> ReportSchedules { get; set; } = new List<ReportSchedule>();
}
