﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class ReportDataV2
{
    public Guid Oid { get; set; }

    public int? Gcrecord { get; set; }

    public int? OptimisticLockField { get; set; }

    public string? DisplayName { get; set; }

    public bool? IsInplaceReport { get; set; }

    public string? DataTypeName { get; set; }

    public string? PredefinedReportTypeName { get; set; }

    public byte[]? Content { get; set; }

    public string? ParametersObjectTypeName { get; set; }

    public int Id { get; set; }
}
