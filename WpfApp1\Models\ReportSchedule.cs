﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class ReportSchedule
{
    public int Id { get; set; }

    public int ReportId { get; set; }

    public string Title { get; set; } = null!;

    public string Frequency { get; set; } = null!;

    public int? DayOfWeek { get; set; }

    public int? DayOfMonth { get; set; }

    public int Hour { get; set; }

    public int Minute { get; set; }

    public string Recipients { get; set; } = null!;

    public bool IsActive { get; set; }

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public long? LastExecutionAt { get; set; }

    public long NextExecutionAt { get; set; }

    public virtual User CreatedByNavigation { get; set; } = null!;

    public virtual Report Report { get; set; } = null!;
}
