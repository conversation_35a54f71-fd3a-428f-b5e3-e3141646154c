﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Resource
{
    public Guid Oid { get; set; }

    public int? Gcrecord { get; set; }

    public int? OptimisticLockField { get; set; }

    public string? ResourceName { get; set; }

    public int? Color { get; set; }

    public byte[]? Image { get; set; }

    public string? Key { get; set; }

    public string? Caption { get; set; }

    public int? ColorInt { get; set; }

    public virtual ICollection<EventResource> EventResources { get; set; } = new List<EventResource>();
}
