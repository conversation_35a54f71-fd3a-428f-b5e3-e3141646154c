﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Role
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string DisplayName { get; set; } = null!;

    public string? Description { get; set; }

    public int Level { get; set; }

    public bool IsSystemRole { get; set; }

    public bool IsActive { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    public virtual ICollection<RoleDefaultPermission> RoleDefaultPermissions { get; set; } = new List<RoleDefaultPermission>();

    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
