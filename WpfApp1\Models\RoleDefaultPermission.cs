﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class RoleDefaultPermission
{
    public int Id { get; set; }

    public int RoleId { get; set; }

    public int PermissionId { get; set; }

    public bool IsActive { get; set; }

    public long CreatedAt { get; set; }

    public virtual Permission Permission { get; set; } = null!;

    public virtual Role Role { get; set; } = null!;
}
