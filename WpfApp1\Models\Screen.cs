﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Screen
{
    public int Id { get; set; }

    public string ScreenName { get; set; } = null!;

    public string ScreenCode { get; set; } = null!;

    public string? Description { get; set; }

    public bool IsActive { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public virtual ICollection<Permission> Permissions { get; set; } = new List<Permission>();

    public virtual ICollection<ScreenAction> ScreenActions { get; set; } = new List<ScreenAction>();
}
