﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class ScreenAction
{
    public int Id { get; set; }

    public int ScreenId { get; set; }

    public int ActionId { get; set; }

    public bool IsActive { get; set; }

    public long CreatedAt { get; set; }

    public virtual Action Action { get; set; } = null!;

    public virtual Screen Screen { get; set; } = null!;
}
