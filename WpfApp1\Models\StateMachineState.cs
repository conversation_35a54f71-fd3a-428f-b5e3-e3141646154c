﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class StateMachineState
{
    public Guid Id { get; set; }

    public string? Caption { get; set; }

    public string? Name { get; set; }

    public Guid? StateMachineId { get; set; }

    public int? Gcrecord { get; set; }

    public int? MarkerValue { get; set; }

    public Guid? StateMachineId1 { get; set; }

    public Guid? StartStateId { get; set; }
}
