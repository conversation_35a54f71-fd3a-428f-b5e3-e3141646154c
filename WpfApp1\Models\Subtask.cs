﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Subtask
{
    public int Id { get; set; }

    public int TaskId { get; set; }

    public string Title { get; set; } = null!;

    public bool IsCompleted { get; set; }

    public long CreatedAt { get; set; }

    public long? CompletedAt { get; set; }

    public virtual Task Task { get; set; } = null!;
}
