﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class SystemSetting
{
    public int Id { get; set; }

    public string SettingKey { get; set; } = null!;

    public string SettingValue { get; set; } = null!;

    public string? SettingGroup { get; set; }

    public string? Description { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public virtual User? CreatedByNavigation { get; set; }
}
