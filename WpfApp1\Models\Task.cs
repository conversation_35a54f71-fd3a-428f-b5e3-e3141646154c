﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class Task
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public int? TaskTypeId { get; set; }

    public int CreatorId { get; set; }

    public int? AssigneeId { get; set; }

    public int? DepartmentId { get; set; }

    public long CreatedAt { get; set; }

    public long? StartDate { get; set; }

    public long? DueDate { get; set; }

    public long? CompletedAt { get; set; }

    public string Status { get; set; } = null!;

    public string Priority { get; set; } = null!;

    public int CompletionPercentage { get; set; }

    public int? EstimatedTime { get; set; }

    public int? ActualTime { get; set; }

    public bool IsDeleted { get; set; }

    public string? Incoming { get; set; }

    public string? Note { get; set; }

    public virtual ICollection<ActivityLog> ActivityLogs { get; set; } = new List<ActivityLog>();

    public virtual User? Assignee { get; set; }

    public virtual ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();

    public virtual ICollection<CalendarEvent> CalendarEvents { get; set; } = new List<CalendarEvent>();

    public virtual User Creator { get; set; } = null!;

    public virtual Department? Department { get; set; }

    public virtual ICollection<Notification> Notifications { get; set; } = new List<Notification>();

    public virtual ICollection<Subtask> Subtasks { get; set; } = new List<Subtask>();

    public virtual ICollection<TaskComment> TaskComments { get; set; } = new List<TaskComment>();

    public virtual ICollection<TaskDocument> TaskDocuments { get; set; } = new List<TaskDocument>();

    public virtual ICollection<TaskHistory> TaskHistories { get; set; } = new List<TaskHistory>();

    public virtual ICollection<TaskMessage> TaskMessages { get; set; } = new List<TaskMessage>();

    public virtual ICollection<TaskProgressTracker> TaskProgressTrackers { get; set; } = new List<TaskProgressTracker>();

    public virtual TaskType? TaskType { get; set; }

    public virtual ICollection<TimeTrackingEntry> TimeTrackingEntries { get; set; } = new List<TimeTrackingEntry>();

    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
