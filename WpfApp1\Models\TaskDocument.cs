﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class TaskDocument
{
    public int Id { get; set; }

    public int TaskId { get; set; }

    public int ArchiveDocumentId { get; set; }

    public string Type { get; set; } = null!;

    public string? Description { get; set; }

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public bool IsShared { get; set; }

    public string Permission { get; set; } = null!;

    public virtual ArchiveDocument ArchiveDocument { get; set; } = null!;

    public virtual User CreatedByNavigation { get; set; } = null!;

    public virtual Task Task { get; set; } = null!;
}
