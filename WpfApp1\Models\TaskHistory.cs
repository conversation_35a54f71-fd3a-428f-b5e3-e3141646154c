﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class TaskHistory
{
    public int Id { get; set; }

    public int TaskId { get; set; }

    public int UserId { get; set; }

    public string Action { get; set; } = null!;

    public string? Details { get; set; }

    public long Timestamp { get; set; }

    public string? ChangeType { get; set; }

    public string? ChangeDescription { get; set; }

    public string? OldValue { get; set; }

    public string? NewValue { get; set; }

    public int? ChangedBy { get; set; }

    public long? ChangedAt { get; set; }

    public virtual User? ChangedByNavigation { get; set; }

    public virtual Task Task { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
