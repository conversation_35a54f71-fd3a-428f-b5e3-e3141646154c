﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

/// <summary>
/// جدول رسائل المهام - يحتوي على جميع الرسائل والمحادثات الخاصة بالمهام
/// </summary>
public partial class TaskMessage
{
    public int Id { get; set; }

    public int TaskId { get; set; }

    public int SenderId { get; set; }

    public string Content { get; set; } = null!;

    public int ContentType { get; set; }

    public int? ReplyToMessageId { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public bool IsRead { get; set; }

    public bool IsPinned { get; set; }

    public long? PinnedAt { get; set; }

    public int? PinnedBy { get; set; }

    public int Priority { get; set; }

    public bool IsMarkedForFollowUp { get; set; }

    public long? FollowUpAt { get; set; }

    public int? MarkedForFollowUpBy { get; set; }

    public bool IsEdited { get; set; }

    public string? MentionedUserIds { get; set; }

    public string? AttachmentIds { get; set; }

    public virtual ICollection<TaskMessage> InverseReplyToMessage { get; set; } = new List<TaskMessage>();

    public virtual User? MarkedForFollowUpByNavigation { get; set; }

    public virtual User? PinnedByNavigation { get; set; }

    public virtual TaskMessage? ReplyToMessage { get; set; }

    public virtual User Sender { get; set; } = null!;

    public virtual Task Task { get; set; } = null!;

    public virtual ICollection<TaskMessageRead> TaskMessageReads { get; set; } = new List<TaskMessageRead>();
}
