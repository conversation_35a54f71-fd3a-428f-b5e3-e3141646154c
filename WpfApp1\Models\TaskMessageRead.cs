﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

/// <summary>
/// جدول قراءة رسائل المهام - يتتبع حالة قراءة كل رسالة لكل مستخدم
/// </summary>
public partial class TaskMessageRead
{
    public int Id { get; set; }

    public int MessageId { get; set; }

    public int UserId { get; set; }

    public long ReadAt { get; set; }

    public bool IsDelivered { get; set; }

    public long? DeliveredAt { get; set; }

    public virtual TaskMessage Message { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
