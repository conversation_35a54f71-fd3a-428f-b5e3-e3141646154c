﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class TaskPriority
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public int Level { get; set; }

    public string? Color { get; set; }

    public string? Icon { get; set; }

    public bool IsDefault { get; set; }

    public long CreatedAt { get; set; }

    public bool IsActive { get; set; }

    public long? UpdatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public virtual User? CreatedByNavigation { get; set; }
}
