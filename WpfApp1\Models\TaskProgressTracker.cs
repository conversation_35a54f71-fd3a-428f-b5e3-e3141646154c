﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class TaskProgressTracker
{
    public int Id { get; set; }

    public int TaskId { get; set; }

    public int Progress { get; set; }

    public long UpdatedAt { get; set; }

    public int UpdatedBy { get; set; }

    public decimal ProgressPercentage { get; set; }

    public string? Notes { get; set; }

    public virtual Task Task { get; set; } = null!;

    public virtual User UpdatedByNavigation { get; set; } = null!;
}
