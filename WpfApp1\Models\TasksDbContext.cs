﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace WpfApp1.Models;

public partial class TasksDbContext : DbContext
{
    public TasksDbContext()
    {
    }

    public TasksDbContext(DbContextOptions<TasksDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Action> Actions { get; set; }

    public virtual DbSet<ActivityLog> ActivityLogs { get; set; }

    public virtual DbSet<ArchiveCategory> ArchiveCategories { get; set; }

    public virtual DbSet<ArchiveDocument> ArchiveDocuments { get; set; }

    public virtual DbSet<ArchiveTag> ArchiveTags { get; set; }

    public virtual DbSet<Attachment> Attachments { get; set; }

    public virtual DbSet<AuditDataItemPersistent> AuditDataItemPersistents { get; set; }

    public virtual DbSet<Backup> Backups { get; set; }

    public virtual DbSet<CalendarEvent> CalendarEvents { get; set; }

    public virtual DbSet<ChatGroup> ChatGroups { get; set; }

    public virtual DbSet<CustomRole> CustomRoles { get; set; }

    public virtual DbSet<CustomRolePermission> CustomRolePermissions { get; set; }

    public virtual DbSet<Dashboard> Dashboards { get; set; }

    public virtual DbSet<DashboardWidget> DashboardWidgets { get; set; }

    public virtual DbSet<Department> Departments { get; set; }

    public virtual DbSet<Event> Events { get; set; }

    public virtual DbSet<EventResource> EventResources { get; set; }

    public virtual DbSet<FailedLoginAttempt> FailedLoginAttempts { get; set; }

    public virtual DbSet<FileDatum> FileData { get; set; }

    public virtual DbSet<GroupMember> GroupMembers { get; set; }

    public virtual DbSet<LoginHistory> LoginHistories { get; set; }

    public virtual DbSet<Message> Messages { get; set; }

    public virtual DbSet<MessageAttachment> MessageAttachments { get; set; }

    public virtual DbSet<MessageReaction> MessageReactions { get; set; }

    public virtual DbSet<MessageRead> MessageReads { get; set; }

    public virtual DbSet<Notification> Notifications { get; set; }

    public virtual DbSet<NotificationSetting> NotificationSettings { get; set; }

    public virtual DbSet<PasswordResetToken> PasswordResetTokens { get; set; }

    public virtual DbSet<Permission> Permissions { get; set; }

    public virtual DbSet<PermissionsBackupBeforeCleanup> PermissionsBackupBeforeCleanups { get; set; }

    public virtual DbSet<PermissionsBackupFinal> PermissionsBackupFinals { get; set; }

    public virtual DbSet<RefreshToken> RefreshTokens { get; set; }

    public virtual DbSet<Reminder> Reminders { get; set; }

    public virtual DbSet<Report> Reports { get; set; }

    public virtual DbSet<ReportDataV2> ReportDataV2s { get; set; }

    public virtual DbSet<ReportSchedule> ReportSchedules { get; set; }

    public virtual DbSet<Resource> Resources { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<RoleDefaultPermission> RoleDefaultPermissions { get; set; }

    public virtual DbSet<RoleDefaultPermissionsBackup> RoleDefaultPermissionsBackups { get; set; }

    public virtual DbSet<RoleMigrationMapping> RoleMigrationMappings { get; set; }

    public virtual DbSet<Screen> Screens { get; set; }

    public virtual DbSet<ScreenAction> ScreenActions { get; set; }

    public virtual DbSet<StateMachineState> StateMachineStates { get; set; }

    public virtual DbSet<Subtask> Subtasks { get; set; }

    public virtual DbSet<SystemLog> SystemLogs { get; set; }

    public virtual DbSet<SystemSetting> SystemSettings { get; set; }

    public virtual DbSet<Task> Tasks { get; set; }

    public virtual DbSet<TaskComment> TaskComments { get; set; }

    public virtual DbSet<TaskDocument> TaskDocuments { get; set; }

    public virtual DbSet<TaskHistory> TaskHistories { get; set; }

    public virtual DbSet<TaskMessage> TaskMessages { get; set; }

    public virtual DbSet<TaskMessageRead> TaskMessageReads { get; set; }

    public virtual DbSet<TaskPriority> TaskPriorities { get; set; }

    public virtual DbSet<TaskProgressTracker> TaskProgressTrackers { get; set; }

    public virtual DbSet<TaskStatus> TaskStatuses { get; set; }

    public virtual DbSet<TaskType> TaskTypes { get; set; }

    public virtual DbSet<TimeTrackingEntry> TimeTrackingEntries { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserCustomRole> UserCustomRoles { get; set; }

    public virtual DbSet<UserPermission> UserPermissions { get; set; }

    public virtual DbSet<UserPermissionsBackup> UserPermissionsBackups { get; set; }

    public virtual DbSet<VwTaskDocumentsWithDetail> VwTaskDocumentsWithDetails { get; set; }

    public virtual DbSet<VwUserAllPermission> VwUserAllPermissions { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Data Source=.\\sqlexpress;Initial Catalog=databasetasks;Integrated Security=True;Encrypt=False;Trust Server Certificate=True");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Action>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__actions__3213E83FD1F64602");

            entity.ToTable("actions");

            entity.HasIndex(e => e.ActionCode, "UQ__actions__BFFF1CB80E2F98E2").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ActionCode)
                .HasMaxLength(50)
                .HasColumnName("action_code");
            entity.Property(e => e.ActionName)
                .HasMaxLength(50)
                .HasColumnName("action_name");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(datediff(second,'1970-01-01',getutcdate()))")
                .HasColumnName("created_at");
            entity.Property(e => e.Description)
                .HasMaxLength(255)
                .HasColumnName("description");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
        });

        modelBuilder.Entity<ActivityLog>(entity =>
        {
            entity.ToTable("activity_logs", tb => tb.HasComment("جدول سجلات الأنشطة - يحتوي على جميع الأنشطة والتغييرات في النظام"));

            entity.HasIndex(e => e.Action, "IX_activity_logs_action");

            entity.HasIndex(e => e.ChangeType, "IX_activity_logs_change_type");

            entity.HasIndex(e => e.ChangedAt, "IX_activity_logs_changed_at").IsDescending();

            entity.HasIndex(e => e.TaskId, "IX_activity_logs_task_id");

            entity.HasIndex(e => e.Timestamp, "IX_activity_logs_timestamp").IsDescending();

            entity.HasIndex(e => e.UserId, "IX_activity_logs_user_id");

            entity.Property(e => e.Id)
                .HasComment("المعرف الفريد لسجل النشاط")
                .HasColumnName("id");
            entity.Property(e => e.Action)
                .HasMaxLength(100)
                .HasComment("نوع الإجراء المنفذ (created, updated, deleted, etc.)")
                .HasColumnName("action");
            entity.Property(e => e.ChangeDescription)
                .HasMaxLength(500)
                .HasColumnName("change_description");
            entity.Property(e => e.ChangeType)
                .HasMaxLength(50)
                .HasColumnName("change_type");
            entity.Property(e => e.ChangedAt).HasColumnName("changed_at");
            entity.Property(e => e.ChangedBy).HasColumnName("changed_by");
            entity.Property(e => e.Details)
                .HasComment("تفاصيل إضافية عن النشاط")
                .HasColumnName("details");
            entity.Property(e => e.NewValue).HasColumnName("new_value");
            entity.Property(e => e.OldValue).HasColumnName("old_value");
            entity.Property(e => e.TaskId)
                .HasComment("معرف المهمة المرتبطة بالنشاط (0 للأنشطة العامة)")
                .HasColumnName("task_id");
            entity.Property(e => e.Timestamp)
                .HasComment("الطابع الزمني للنشاط (Unix timestamp)")
                .HasColumnName("timestamp");
            entity.Property(e => e.UserId)
                .HasComment("معرف المستخدم الذي قام بالنشاط")
                .HasColumnName("user_id");

            entity.HasOne(d => d.ChangedByNavigation).WithMany(p => p.ActivityLogChangedByNavigations)
                .HasForeignKey(d => d.ChangedBy)
                .HasConstraintName("FK_activity_logs_changed_by");

            entity.HasOne(d => d.Task).WithMany(p => p.ActivityLogs)
                .HasForeignKey(d => d.TaskId)
                .HasConstraintName("FK_activity_logs_tasks");

            entity.HasOne(d => d.User).WithMany(p => p.ActivityLogUsers)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_activity_logs_users");
        });

        modelBuilder.Entity<ArchiveCategory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__archive___3213E83F1C467069");

            entity.ToTable("archive_categories");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.ParentId).HasColumnName("parent_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.ArchiveCategories)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_archive_categories_users");

            entity.HasOne(d => d.Parent).WithMany(p => p.InverseParent)
                .HasForeignKey(d => d.ParentId)
                .HasConstraintName("FK_archive_categories_parent");
        });

        modelBuilder.Entity<ArchiveDocument>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__archive___3213E83F98BCFBC5");

            entity.ToTable("archive_documents");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CategoryId).HasColumnName("category_id");
            entity.Property(e => e.Content)
                .HasColumnType("ntext")
                .HasColumnName("content");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy)
                .HasDefaultValue(1)
                .HasColumnName("created_by");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.FileName)
                .HasMaxLength(255)
                .HasColumnName("file_name");
            entity.Property(e => e.FilePath)
                .HasMaxLength(255)
                .HasColumnName("file_path");
            entity.Property(e => e.FileSize).HasColumnName("file_size");
            entity.Property(e => e.FileType)
                .HasMaxLength(50)
                .HasColumnName("file_type");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.Metadata).HasColumnName("metadata");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UploadedAt).HasColumnName("uploaded_at");
            entity.Property(e => e.UploadedBy).HasColumnName("uploaded_by");

            entity.HasOne(d => d.Category).WithMany(p => p.ArchiveDocuments)
                .HasForeignKey(d => d.CategoryId)
                .HasConstraintName("FK_archive_documents_categories");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.ArchiveDocumentCreatedByNavigations)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_archive_documents_created_by_users");

            entity.HasOne(d => d.UploadedByNavigation).WithMany(p => p.ArchiveDocumentUploadedByNavigations)
                .HasForeignKey(d => d.UploadedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_archive_documents_users");

            entity.HasMany(d => d.Tags).WithMany(p => p.Documents)
                .UsingEntity<Dictionary<string, object>>(
                    "ArchiveDocumentTag",
                    r => r.HasOne<ArchiveTag>().WithMany()
                        .HasForeignKey("TagId")
                        .HasConstraintName("FK_archive_document_tags_tag"),
                    l => l.HasOne<ArchiveDocument>().WithMany()
                        .HasForeignKey("DocumentId")
                        .HasConstraintName("FK_archive_document_tags_doc"),
                    j =>
                    {
                        j.HasKey("DocumentId", "TagId");
                        j.ToTable("archive_document_tags");
                        j.IndexerProperty<int>("DocumentId").HasColumnName("document_id");
                        j.IndexerProperty<int>("TagId").HasColumnName("tag_id");
                    });
        });

        modelBuilder.Entity<ArchiveTag>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__archive___3213E83FE3B96A3A");

            entity.ToTable("archive_tags");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description)
                .HasColumnType("ntext")
                .HasColumnName("description");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.ArchiveTags)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_archive_tags_users");
        });

        modelBuilder.Entity<Attachment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__attachme__3213E83F0E56CFD6");

            entity.ToTable("attachments");

            entity.HasIndex(e => e.FileHash, "IX_Attachments_FileHash");

            entity.HasIndex(e => e.IsBackedUp, "IX_Attachments_IsBackedUp");

            entity.HasIndex(e => e.StorageFolder, "IX_Attachments_StorageFolder");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.FileHash).HasMaxLength(64);
            entity.Property(e => e.FileName)
                .HasMaxLength(255)
                .HasColumnName("file_name");
            entity.Property(e => e.FilePath)
                .HasMaxLength(255)
                .HasColumnName("file_path");
            entity.Property(e => e.FileSize).HasColumnName("file_size");
            entity.Property(e => e.FileType)
                .HasMaxLength(50)
                .HasColumnName("file_type");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.StorageFolder)
                .HasMaxLength(50)
                .HasDefaultValue("attachments");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.UniqueFileName).HasMaxLength(100);
            entity.Property(e => e.UploadedAt).HasColumnName("uploaded_at");
            entity.Property(e => e.UploadedBy).HasColumnName("uploaded_by");

            entity.HasOne(d => d.Task).WithMany(p => p.Attachments)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_attachments_tasks");

            entity.HasOne(d => d.UploadedByNavigation).WithMany(p => p.Attachments)
                .HasForeignKey(d => d.UploadedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_attachments_users");
        });

        modelBuilder.Entity<AuditDataItemPersistent>(entity =>
        {
            entity.HasKey(e => e.Oid).HasName("PK__AuditDat__CB3E4F313262ED4A");

            entity.ToTable("AuditDataItemPersistent");

            entity.Property(e => e.Oid).ValueGeneratedNever();
            entity.Property(e => e.Gcrecord).HasColumnName("GCRecord");
            entity.Property(e => e.ObjectKey).HasMaxLength(100);
            entity.Property(e => e.ObjectType).HasMaxLength(100);
            entity.Property(e => e.PropertyName).HasMaxLength(100);
            entity.Property(e => e.UserName).HasMaxLength(255);
        });

        modelBuilder.Entity<Backup>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__backups__3213E83F76D7E979");

            entity.ToTable("backups");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.FileName)
                .HasMaxLength(255)
                .HasColumnName("file_name");
            entity.Property(e => e.FilePath)
                .HasMaxLength(255)
                .HasColumnName("file_path");
            entity.Property(e => e.FileSize).HasColumnName("file_size");
            entity.Property(e => e.IsAutoBackup).HasColumnName("is_auto_backup");
            entity.Property(e => e.IsRestored).HasColumnName("is_restored");
            entity.Property(e => e.RestoredAt).HasColumnName("restored_at");
            entity.Property(e => e.RestoredBy).HasColumnName("restored_by");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.BackupCreatedByNavigations)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_backups_created_by_users");

            entity.HasOne(d => d.RestoredByNavigation).WithMany(p => p.BackupRestoredByNavigations)
                .HasForeignKey(d => d.RestoredBy)
                .HasConstraintName("FK_backups_restored_by_users");
        });

        modelBuilder.Entity<CalendarEvent>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__calendar__3213E83FBE2C8E42");

            entity.ToTable("calendar_events");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.AllDay).HasColumnName("all_day");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Duration).HasColumnName("duration");
            entity.Property(e => e.EndTime).HasColumnName("end_time");
            entity.Property(e => e.EventType)
                .HasMaxLength(50)
                .HasDefaultValue("other")
                .HasColumnName("event_type");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.Location)
                .HasMaxLength(255)
                .HasColumnName("location");
            entity.Property(e => e.RecurrenceCount).HasColumnName("recurrence_count");
            entity.Property(e => e.RecurrenceEndDate).HasColumnName("recurrence_end_date");
            entity.Property(e => e.RecurrencePattern)
                .HasMaxLength(50)
                .HasDefaultValue("none")
                .HasColumnName("recurrence_pattern");
            entity.Property(e => e.RecurrenceRule).HasColumnName("recurrence_rule");
            entity.Property(e => e.ReminderEnabled).HasColumnName("reminder_enabled");
            entity.Property(e => e.ReminderMinutes).HasColumnName("reminder_minutes");
            entity.Property(e => e.ReminderSent).HasColumnName("reminder_sent");
            entity.Property(e => e.StartTime).HasColumnName("start_time");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.Task).WithMany(p => p.CalendarEvents)
                .HasForeignKey(d => d.TaskId)
                .HasConstraintName("FK_calendar_events_task_id_tasks");

            entity.HasOne(d => d.User).WithMany(p => p.CalendarEvents)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_calendar_events_user_id_users");
        });

        modelBuilder.Entity<ChatGroup>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__chat_gro__3213E83F3877CCEC");

            entity.ToTable("chat_groups");

            entity.HasIndex(e => new { e.IsDeleted, e.IsArchived }, "IX_chat_groups_deleted_archived");

            entity.HasIndex(e => e.GroupType, "IX_chat_groups_group_type");

            entity.HasIndex(e => e.IsArchived, "IX_chat_groups_is_archived");

            entity.HasIndex(e => e.IsDeleted, "IX_chat_groups_is_deleted");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.GroupType)
                .HasMaxLength(50)
                .HasDefaultValue("general")
                .HasColumnName("group_type");
            entity.Property(e => e.ImageUrl)
                .HasMaxLength(255)
                .HasColumnName("image_url");
            entity.Property(e => e.IsArchived).HasColumnName("is_archived");
            entity.Property(e => e.IsDeleted)
                .HasDefaultValue(false)
                .HasColumnName("is_deleted");
            entity.Property(e => e.IsDirectMessage)
                .HasDefaultValue(false)
                .HasColumnName("is_direct_message");
            entity.Property(e => e.IsPrivate)
                .HasDefaultValue(false)
                .HasColumnName("is_private");
            entity.Property(e => e.MaxMembers).HasColumnName("max_members");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.ChatGroups)
                .HasForeignKey(d => d.CreatedBy)
                .HasConstraintName("FK_chat_groups_created_by_users");
        });

        modelBuilder.Entity<CustomRole>(entity =>
        {
            entity.ToTable("custom_roles");

            entity.HasIndex(e => e.CreatedBy, "IX_custom_roles_created_by");

            entity.HasIndex(e => e.UpdatedBy, "IX_custom_roles_updated_by");

            entity.HasIndex(e => e.Name, "UQ_custom_roles_name").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Color)
                .HasMaxLength(7)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description)
                .HasMaxLength(500)
                .HasColumnName("description");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.Level).HasColumnName("level");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UpdatedBy).HasColumnName("updated_by");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.CustomRoleCreatedByNavigations)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_custom_roles_created_by_users");

            entity.HasOne(d => d.UpdatedByNavigation).WithMany(p => p.CustomRoleUpdatedByNavigations)
                .HasForeignKey(d => d.UpdatedBy)
                .OnDelete(DeleteBehavior.SetNull)
                .HasConstraintName("FK_custom_roles_updated_by_users");
        });

        modelBuilder.Entity<CustomRolePermission>(entity =>
        {
            entity.ToTable("custom_role_permissions");

            entity.HasIndex(e => e.CustomRoleId, "IX_custom_role_permissions_custom_role_id");

            entity.HasIndex(e => e.GrantedBy, "IX_custom_role_permissions_granted_by");

            entity.HasIndex(e => e.PermissionId, "IX_custom_role_permissions_permission_id");

            entity.HasIndex(e => new { e.CustomRoleId, e.PermissionId }, "UQ_custom_role_permissions_role_permission").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CustomRoleId).HasColumnName("custom_role_id");
            entity.Property(e => e.ExpiresAt).HasColumnName("expires_at");
            entity.Property(e => e.GrantedAt).HasColumnName("granted_at");
            entity.Property(e => e.GrantedBy).HasColumnName("granted_by");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");

            entity.HasOne(d => d.CustomRole).WithMany(p => p.CustomRolePermissions)
                .HasForeignKey(d => d.CustomRoleId)
                .HasConstraintName("FK_custom_role_permissions_custom_role");

            entity.HasOne(d => d.GrantedByNavigation).WithMany(p => p.CustomRolePermissions)
                .HasForeignKey(d => d.GrantedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_custom_role_permissions_granted_by_user");

            entity.HasOne(d => d.Permission).WithMany(p => p.CustomRolePermissions)
                .HasForeignKey(d => d.PermissionId)
                .HasConstraintName("FK_custom_role_permissions_permission");
        });

        modelBuilder.Entity<Dashboard>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__dashboar__3213E83F0FAA756D");

            entity.ToTable("dashboards");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.GridColumns)
                .HasDefaultValue(12)
                .HasColumnName("grid_columns");
            entity.Property(e => e.GridRows)
                .HasDefaultValue(12)
                .HasColumnName("grid_rows");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.IsDefault).HasColumnName("is_default");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsShared).HasColumnName("is_shared");
            entity.Property(e => e.OwnerId).HasColumnName("owner_id");
            entity.Property(e => e.Settings).HasColumnName("settings");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.Owner).WithMany(p => p.Dashboards)
                .HasForeignKey(d => d.OwnerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_dashboards_owner_id_users");

            entity.HasMany(d => d.Users).WithMany(p => p.DashboardsNavigation)
                .UsingEntity<Dictionary<string, object>>(
                    "DashboardShare",
                    r => r.HasOne<User>().WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_dashboard_shares_user"),
                    l => l.HasOne<Dashboard>().WithMany()
                        .HasForeignKey("DashboardId")
                        .HasConstraintName("FK_dashboard_shares_dashboard"),
                    j =>
                    {
                        j.HasKey("DashboardId", "UserId");
                        j.ToTable("dashboard_shares");
                        j.IndexerProperty<int>("DashboardId").HasColumnName("dashboard_id");
                        j.IndexerProperty<int>("UserId").HasColumnName("user_id");
                    });
        });

        modelBuilder.Entity<DashboardWidget>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__dashboar__3213E83FDC09D64E");

            entity.ToTable("dashboard_widgets");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Configuration).HasColumnName("configuration");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.DashboardId).HasColumnName("dashboard_id");
            entity.Property(e => e.DataSource)
                .HasMaxLength(50)
                .HasColumnName("data_source");
            entity.Property(e => e.Height).HasColumnName("height");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsVisible)
                .HasDefaultValue(true)
                .HasColumnName("is_visible");
            entity.Property(e => e.OrderIndex).HasColumnName("order_index");
            entity.Property(e => e.Position).HasColumnName("position");
            entity.Property(e => e.PositionX).HasColumnName("position_x");
            entity.Property(e => e.PositionY).HasColumnName("position_y");
            entity.Property(e => e.Query).HasColumnName("query");
            entity.Property(e => e.RefreshInterval).HasColumnName("refresh_interval");
            entity.Property(e => e.Settings).HasColumnName("settings");
            entity.Property(e => e.Size).HasColumnName("size");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.WidgetType)
                .HasMaxLength(50)
                .HasColumnName("widget_type");
            entity.Property(e => e.Width).HasColumnName("width");

            entity.HasOne(d => d.Dashboard).WithMany(p => p.DashboardWidgets)
                .HasForeignKey(d => d.DashboardId)
                .HasConstraintName("FK_dashboard_widgets_dashboards");
        });

        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__departme__3213E83FED2D55C0");

            entity.ToTable("departments");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.Level).HasColumnName("level");
            entity.Property(e => e.ManagerId).HasColumnName("manager_id");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.ParentId).HasColumnName("parent_id");
            entity.Property(e => e.SortOrder).HasColumnName("sort_order");

            entity.HasOne(d => d.Manager).WithMany(p => p.Departments)
                .HasForeignKey(d => d.ManagerId)
                .HasConstraintName("FK_departments_users");

            entity.HasOne(d => d.Parent).WithMany(p => p.InverseParent)
                .HasForeignKey(d => d.ParentId)
                .HasConstraintName("FK_Departments_Parent");
        });

        modelBuilder.Entity<Event>(entity =>
        {
            entity.HasKey(e => e.Oid).HasName("PK__Event__CB3E4F31B72E822B");

            entity.ToTable("Event");

            entity.Property(e => e.Oid).ValueGeneratedNever();
            entity.Property(e => e.CreatedBy).HasMaxLength(255);
            entity.Property(e => e.Gcrecord).HasColumnName("GCRecord");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(255);
            entity.Property(e => e.Location).HasMaxLength(255);
            entity.Property(e => e.ObjectType).HasMaxLength(100);
            entity.Property(e => e.RecurrencePatternId).HasColumnName("RecurrencePatternID");
            entity.Property(e => e.Subject).HasMaxLength(255);
        });

        modelBuilder.Entity<EventResource>(entity =>
        {
            entity.HasKey(e => e.Oid).HasName("PK__EventRes__CB3E4F31A2DC8E56");

            entity.ToTable("EventResource");

            entity.Property(e => e.Oid).ValueGeneratedNever();
            entity.Property(e => e.Gcrecord).HasColumnName("GCRecord");

            entity.HasOne(d => d.EventNavigation).WithMany(p => p.EventResources)
                .HasForeignKey(d => d.Event)
                .HasConstraintName("FK_EventResource_Event");

            entity.HasOne(d => d.ResourceNavigation).WithMany(p => p.EventResources)
                .HasForeignKey(d => d.Resource)
                .HasConstraintName("FK_EventResource_Resource");
        });

        modelBuilder.Entity<FailedLoginAttempt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__failed_l__3213E83F995E6514");

            entity.ToTable("failed_login_attempts");

            entity.HasIndex(e => e.AttemptTime, "IX_failed_login_attempts_attempt_time");

            entity.HasIndex(e => e.EmailOrUsername, "IX_failed_login_attempts_email_username");

            entity.HasIndex(e => e.IpAddress, "IX_failed_login_attempts_ip_address");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.AttemptTime).HasColumnName("attempt_time");
            entity.Property(e => e.EmailOrUsername)
                .HasMaxLength(255)
                .HasColumnName("email_or_username");
            entity.Property(e => e.FailureReason)
                .HasMaxLength(255)
                .HasColumnName("failure_reason");
            entity.Property(e => e.IpAddress)
                .HasMaxLength(45)
                .HasColumnName("ip_address");
            entity.Property(e => e.UserAgent)
                .HasMaxLength(500)
                .HasColumnName("user_agent");
        });

        modelBuilder.Entity<FileDatum>(entity =>
        {
            entity.HasKey(e => e.Oid).HasName("PK__FileData__CB3E4F31B5C2F2BA");

            entity.Property(e => e.Oid).ValueGeneratedNever();
            entity.Property(e => e.FileName).HasMaxLength(255);
            entity.Property(e => e.Gcrecord).HasColumnName("GCRecord");
        });

        modelBuilder.Entity<GroupMember>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__group_me__3213E83F8B2870C3");

            entity.ToTable("group_members");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.GroupId).HasColumnName("group_id");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.JoinedAt).HasColumnName("joined_at");
            entity.Property(e => e.LeftAt).HasColumnName("left_at");
            entity.Property(e => e.Role).HasColumnName("role");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.Group).WithMany(p => p.GroupMembers)
                .HasForeignKey(d => d.GroupId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_group_members_group_id_chat_groups");

            entity.HasOne(d => d.User).WithMany(p => p.GroupMembers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_group_members_user_id_users");
        });

        modelBuilder.Entity<LoginHistory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__login_hi__3213E83F4690A658");

            entity.ToTable("login_history");

            entity.HasIndex(e => e.IsSuccessful, "IX_login_history_is_successful");

            entity.HasIndex(e => e.LoginTime, "IX_login_history_login_time");

            entity.HasIndex(e => e.UserId, "IX_login_history_user_id");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.FailureReason)
                .HasMaxLength(255)
                .HasColumnName("failure_reason");
            entity.Property(e => e.IpAddress)
                .HasMaxLength(45)
                .HasColumnName("ip_address");
            entity.Property(e => e.IsSuccessful).HasColumnName("is_successful");
            entity.Property(e => e.LoginTime).HasColumnName("login_time");
            entity.Property(e => e.LogoutTime).HasColumnName("logout_time");
            entity.Property(e => e.SessionDuration).HasColumnName("session_duration");
            entity.Property(e => e.UserAgent)
                .HasMaxLength(500)
                .HasColumnName("user_agent");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.User).WithMany(p => p.LoginHistories)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__login_his__user___7FEAFD3E");
        });

        modelBuilder.Entity<Message>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__messages__3213E83FF2152609");

            entity.ToTable("messages");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Content).HasColumnName("content");
            entity.Property(e => e.ContentType).HasColumnName("content_type");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.FollowUpAt).HasColumnName("follow_up_at");
            entity.Property(e => e.GroupId).HasColumnName("group_id");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsEdited).HasColumnName("is_edited");
            entity.Property(e => e.IsMarkedForFollowUp).HasColumnName("is_marked_for_follow_up");
            entity.Property(e => e.IsPinned).HasColumnName("is_pinned");
            entity.Property(e => e.IsRead).HasColumnName("is_read");
            entity.Property(e => e.MarkedForFollowUpBy).HasColumnName("marked_for_follow_up_by");
            entity.Property(e => e.PinnedAt).HasColumnName("pinned_at");
            entity.Property(e => e.PinnedBy).HasColumnName("pinned_by");
            entity.Property(e => e.Priority).HasColumnName("priority");
            entity.Property(e => e.ReceiverId).HasColumnName("receiver_id");
            entity.Property(e => e.ReplyToMessageId).HasColumnName("reply_to_message_id");
            entity.Property(e => e.SenderId).HasColumnName("sender_id");
            entity.Property(e => e.SentAt).HasColumnName("sent_at");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.Group).WithMany(p => p.Messages)
                .HasForeignKey(d => d.GroupId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_messages_group_id_chat_groups");

            entity.HasOne(d => d.MarkedForFollowUpByNavigation).WithMany(p => p.MessageMarkedForFollowUpByNavigations)
                .HasForeignKey(d => d.MarkedForFollowUpBy)
                .HasConstraintName("FK_messages_marked_for_follow_up_by_users");

            entity.HasOne(d => d.PinnedByNavigation).WithMany(p => p.MessagePinnedByNavigations)
                .HasForeignKey(d => d.PinnedBy)
                .HasConstraintName("FK_messages_pinned_by_users");

            entity.HasOne(d => d.Receiver).WithMany(p => p.MessageReceivers)
                .HasForeignKey(d => d.ReceiverId)
                .HasConstraintName("FK_messages_receiver_id_users");

            entity.HasOne(d => d.ReplyToMessage).WithMany(p => p.InverseReplyToMessage)
                .HasForeignKey(d => d.ReplyToMessageId)
                .HasConstraintName("FK_messages_reply_to_message_id_messages");

            entity.HasOne(d => d.Sender).WithMany(p => p.MessageSenders)
                .HasForeignKey(d => d.SenderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_messages_sender_id_users");

            entity.HasMany(d => d.Users).WithMany(p => p.Messages)
                .UsingEntity<Dictionary<string, object>>(
                    "MessageMention",
                    r => r.HasOne<User>().WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("FK_message_mentions_user"),
                    l => l.HasOne<Message>().WithMany()
                        .HasForeignKey("MessageId")
                        .HasConstraintName("FK_message_mentions_message"),
                    j =>
                    {
                        j.HasKey("MessageId", "UserId");
                        j.ToTable("message_mentions");
                        j.IndexerProperty<int>("MessageId").HasColumnName("message_id");
                        j.IndexerProperty<int>("UserId").HasColumnName("user_id");
                    });
        });

        modelBuilder.Entity<MessageAttachment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__message___3213E83FC0CE6F27");

            entity.ToTable("message_attachments");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.FileName)
                .HasMaxLength(255)
                .HasColumnName("file_name");
            entity.Property(e => e.FilePath)
                .HasMaxLength(255)
                .HasColumnName("file_path");
            entity.Property(e => e.FileSize).HasColumnName("file_size");
            entity.Property(e => e.FileType)
                .HasMaxLength(50)
                .HasColumnName("file_type");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.MessageId).HasColumnName("message_id");
            entity.Property(e => e.UploadedAt).HasColumnName("uploaded_at");
            entity.Property(e => e.UploadedBy).HasColumnName("uploaded_by");

            entity.HasOne(d => d.Message).WithMany(p => p.MessageAttachments)
                .HasForeignKey(d => d.MessageId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_message_attachments_message_id_messages");

            entity.HasOne(d => d.UploadedByNavigation).WithMany(p => p.MessageAttachments)
                .HasForeignKey(d => d.UploadedBy)
                .HasConstraintName("FK_message_attachments_uploaded_by_users");
        });

        modelBuilder.Entity<MessageReaction>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__message___3213E83F6A4A7E34");

            entity.ToTable("message_reactions");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.MessageId).HasColumnName("message_id");
            entity.Property(e => e.Reaction)
                .HasMaxLength(10)
                .HasColumnName("reaction");
            entity.Property(e => e.ReactionType)
                .HasMaxLength(50)
                .HasColumnName("reaction_type");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.Message).WithMany(p => p.MessageReactions)
                .HasForeignKey(d => d.MessageId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_message_reactions_message_id_messages");

            entity.HasOne(d => d.User).WithMany(p => p.MessageReactions)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_message_reactions_user_id_users");
        });

        modelBuilder.Entity<MessageRead>(entity =>
        {
            entity.ToTable("message_reads", tb => tb.HasComment("جدول قراءة الرسائل العامة - يتتبع حالة قراءة كل رسالة لكل مستخدم في المحادثات العامة"));

            entity.HasIndex(e => e.MessageId, "IX_message_reads_message_id");

            entity.HasIndex(e => e.ReadAt, "IX_message_reads_read_at");

            entity.HasIndex(e => e.UserId, "IX_message_reads_user_id");

            entity.HasIndex(e => new { e.MessageId, e.UserId }, "UQ_message_reads_message_user").IsUnique();

            entity.Property(e => e.Id)
                .HasComment("معرف فريد لحالة القراءة")
                .HasColumnName("id");
            entity.Property(e => e.MessageId)
                .HasComment("معرف الرسالة المقروءة")
                .HasColumnName("message_id");
            entity.Property(e => e.ReadAt)
                .HasComment("تاريخ قراءة الرسالة (Unix timestamp)")
                .HasColumnName("read_at");
            entity.Property(e => e.UserId)
                .HasComment("معرف المستخدم الذي قرأ الرسالة")
                .HasColumnName("user_id");

            entity.HasOne(d => d.Message).WithMany(p => p.MessageReads)
                .HasForeignKey(d => d.MessageId)
                .HasConstraintName("FK_message_reads_message");

            entity.HasOne(d => d.User).WithMany(p => p.MessageReads)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_message_reads_user");
        });

        modelBuilder.Entity<Notification>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__notifica__3213E83F6CE68BF2");

            entity.ToTable("notifications");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Content).HasColumnName("content");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.IsRead).HasColumnName("is_read");
            entity.Property(e => e.RelatedId).HasColumnName("related_id");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.Type)
                .HasMaxLength(50)
                .HasColumnName("type");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.Related).WithMany(p => p.Notifications)
                .HasForeignKey(d => d.RelatedId)
                .HasConstraintName("FK_notifications_To_task");

            entity.HasOne(d => d.User).WithMany(p => p.Notifications)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_notifications_user_id_users");
        });

        modelBuilder.Entity<NotificationSetting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__notifica__3213E83F7689570C");

            entity.ToTable("notification_settings");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.DeliveryMethod)
                .HasMaxLength(20)
                .HasDefaultValue("app")
                .HasColumnName("delivery_method");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsEmailEnabled)
                .HasDefaultValue(true)
                .HasColumnName("is_email_enabled");
            entity.Property(e => e.IsEnabled)
                .HasDefaultValue(true)
                .HasColumnName("is_enabled");
            entity.Property(e => e.IsPushEnabled)
                .HasDefaultValue(true)
                .HasColumnName("is_push_enabled");
            entity.Property(e => e.IsSmsEnabled).HasColumnName("is_sms_enabled");
            entity.Property(e => e.NotificationType)
                .HasMaxLength(50)
                .HasColumnName("notification_type");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.User).WithMany(p => p.NotificationSettings)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_notification_settings_user_id_users");
        });

        modelBuilder.Entity<PasswordResetToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__password__3213E83FE7FE8935");

            entity.ToTable("password_reset_tokens");

            entity.HasIndex(e => e.ExpiresAt, "IX_password_reset_tokens_expires_at");

            entity.HasIndex(e => e.Token, "IX_password_reset_tokens_token");

            entity.HasIndex(e => e.UserId, "IX_password_reset_tokens_user_id");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.ExpiresAt).HasColumnName("expires_at");
            entity.Property(e => e.IsUsed)
                .HasDefaultValue(false)
                .HasColumnName("is_used");
            entity.Property(e => e.Token)
                .HasMaxLength(500)
                .HasColumnName("token");
            entity.Property(e => e.UsedAt).HasColumnName("used_at");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.User).WithMany(p => p.PasswordResetTokens)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__password___user___05A3D694");
        });

        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__permissi__3213E83F722E0A3D");

            entity.ToTable("permissions");

            entity.HasIndex(e => e.Name, "IX_permissions_name_unique").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ActionId).HasColumnName("action_id");
            entity.Property(e => e.Category)
                .HasMaxLength(100)
                .HasColumnName("category");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.IsDefault).HasColumnName("is_default");
            entity.Property(e => e.Level)
                .HasDefaultValue(1)
                .HasColumnName("level");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.PermissionGroup)
                .HasMaxLength(50)
                .HasColumnName("permission_group");
            entity.Property(e => e.ScreenId).HasColumnName("screen_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.Action).WithMany(p => p.Permissions)
                .HasForeignKey(d => d.ActionId)
                .HasConstraintName("FK_permissions_actions");

            entity.HasOne(d => d.Screen).WithMany(p => p.Permissions)
                .HasForeignKey(d => d.ScreenId)
                .HasConstraintName("FK_permissions_screens");
        });

        modelBuilder.Entity<PermissionsBackupBeforeCleanup>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("permissions_backup_before_cleanup");

            entity.Property(e => e.ActionId).HasColumnName("action_id");
            entity.Property(e => e.Category)
                .HasMaxLength(100)
                .HasColumnName("category");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("id");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
            entity.Property(e => e.IsDefault).HasColumnName("is_default");
            entity.Property(e => e.Level).HasColumnName("level");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.PermissionGroup)
                .HasMaxLength(50)
                .HasColumnName("permission_group");
            entity.Property(e => e.ScreenId).HasColumnName("screen_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
        });

        modelBuilder.Entity<PermissionsBackupFinal>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("permissions_backup_final");

            entity.Property(e => e.ActionId).HasColumnName("action_id");
            entity.Property(e => e.Category)
                .HasMaxLength(100)
                .HasColumnName("category");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("id");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
            entity.Property(e => e.IsDefault).HasColumnName("is_default");
            entity.Property(e => e.Level).HasColumnName("level");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.PermissionGroup)
                .HasMaxLength(50)
                .HasColumnName("permission_group");
            entity.Property(e => e.ScreenId).HasColumnName("screen_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
        });

        modelBuilder.Entity<RefreshToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__refresh___3213E83FE4BCD338");

            entity.ToTable("refresh_tokens");

            entity.HasIndex(e => e.ExpiresAt, "IX_refresh_tokens_expires_at");

            entity.HasIndex(e => e.Token, "IX_refresh_tokens_token");

            entity.HasIndex(e => e.UserId, "IX_refresh_tokens_user_id");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.ExpiresAt).HasColumnName("expires_at");
            entity.Property(e => e.IsRevoked)
                .HasDefaultValue(false)
                .HasColumnName("is_revoked");
            entity.Property(e => e.ReplacedByToken)
                .HasMaxLength(500)
                .HasColumnName("replaced_by_token");
            entity.Property(e => e.RevokedAt).HasColumnName("revoked_at");
            entity.Property(e => e.Token)
                .HasMaxLength(500)
                .HasColumnName("token");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.User).WithMany(p => p.RefreshTokens)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__refresh_t__user___7D0E9093");
        });

        modelBuilder.Entity<Reminder>(entity =>
        {
            entity.HasKey(e => e.Oid).HasName("PK__Reminder__CB3E4F314389E83D");

            entity.ToTable("Reminder");

            entity.Property(e => e.Oid).ValueGeneratedNever();
            entity.Property(e => e.Gcrecord).HasColumnName("GCRecord");

            entity.HasOne(d => d.EventNavigation).WithMany(p => p.Reminders)
                .HasForeignKey(d => d.Event)
                .HasConstraintName("FK_Reminder_Event");
        });

        modelBuilder.Entity<Report>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__reports__3213E83F1D27A696");

            entity.ToTable("reports");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsPublic).HasColumnName("is_public");
            entity.Property(e => e.Parameters).HasColumnName("parameters");
            entity.Property(e => e.Query).HasColumnName("query");
            entity.Property(e => e.ReportType)
                .HasMaxLength(50)
                .HasColumnName("report_type");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.Reports)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_reports_created_by_users");
        });

        modelBuilder.Entity<ReportDataV2>(entity =>
        {
            entity.HasKey(e => e.Oid).HasName("PK__ReportDa__CB3E4F31DDA5BB8A");

            entity.ToTable("ReportDataV2");

            entity.Property(e => e.Oid).ValueGeneratedNever();
            entity.Property(e => e.DataTypeName).HasMaxLength(255);
            entity.Property(e => e.DisplayName).HasMaxLength(255);
            entity.Property(e => e.Gcrecord).HasColumnName("GCRecord");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.ParametersObjectTypeName).HasMaxLength(255);
            entity.Property(e => e.PredefinedReportTypeName).HasMaxLength(255);
        });

        modelBuilder.Entity<ReportSchedule>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__report_s__3213E83F2D45617B");

            entity.ToTable("report_schedules");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.DayOfMonth).HasColumnName("day_of_month");
            entity.Property(e => e.DayOfWeek).HasColumnName("day_of_week");
            entity.Property(e => e.Frequency)
                .HasMaxLength(20)
                .HasColumnName("frequency");
            entity.Property(e => e.Hour).HasColumnName("hour");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.LastExecutionAt).HasColumnName("last_execution_at");
            entity.Property(e => e.Minute).HasColumnName("minute");
            entity.Property(e => e.NextExecutionAt).HasColumnName("next_execution_at");
            entity.Property(e => e.Recipients).HasColumnName("recipients");
            entity.Property(e => e.ReportId).HasColumnName("report_id");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.ReportSchedules)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_report_schedules_created_by_users");

            entity.HasOne(d => d.Report).WithMany(p => p.ReportSchedules)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_report_schedules_report_id_reports");
        });

        modelBuilder.Entity<Resource>(entity =>
        {
            entity.HasKey(e => e.Oid).HasName("PK__Resource__CB3E4F319CF269F2");

            entity.ToTable("Resource");

            entity.Property(e => e.Oid).ValueGeneratedNever();
            entity.Property(e => e.Caption).HasMaxLength(200);
            entity.Property(e => e.ColorInt).HasColumnName("Color_Int");
            entity.Property(e => e.Gcrecord).HasColumnName("GCRecord");
            entity.Property(e => e.Key).HasMaxLength(200);
            entity.Property(e => e.ResourceName).HasMaxLength(255);
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("roles");

            entity.HasIndex(e => e.Level, "UQ_roles_level").IsUnique();

            entity.HasIndex(e => e.Name, "UQ_roles_name").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description)
                .HasMaxLength(500)
                .HasColumnName("description");
            entity.Property(e => e.DisplayName)
                .HasMaxLength(100)
                .HasColumnName("display_name");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.IsSystemRole).HasColumnName("is_system_role");
            entity.Property(e => e.Level)
                .HasDefaultValue(1)
                .HasColumnName("level");
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .HasColumnName("name");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UpdatedBy).HasColumnName("updated_by");
        });

        modelBuilder.Entity<RoleDefaultPermission>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__role_def__3213E83F16459FF5");

            entity.ToTable("role_default_permissions");

            entity.HasIndex(e => new { e.RoleId, e.PermissionId }, "UQ__role_def__C85A5462B268BD6C").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(datediff(second,'1970-01-01',getutcdate()))")
                .HasColumnName("created_at");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");
            entity.Property(e => e.RoleId).HasColumnName("role_id");

            entity.HasOne(d => d.Permission).WithMany(p => p.RoleDefaultPermissions)
                .HasForeignKey(d => d.PermissionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__role_defa__permi__33008CF0");

            entity.HasOne(d => d.Role).WithMany(p => p.RoleDefaultPermissions)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__role_defa__role___320C68B7");
        });

        modelBuilder.Entity<RoleDefaultPermissionsBackup>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("role_default_permissions_backup");

            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("id");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");
            entity.Property(e => e.RoleId).HasColumnName("role_id");
        });

        modelBuilder.Entity<RoleMigrationMapping>(entity =>
        {
            entity.HasKey(e => e.OldRoleId).HasName("PK__role_mig__FFFFB968D9299347");

            entity.ToTable("role_migration_mapping");

            entity.Property(e => e.OldRoleId)
                .ValueGeneratedNever()
                .HasColumnName("old_role_id");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(datediff(second,'1970-01-01',getutcdate()))")
                .HasColumnName("created_at");
            entity.Property(e => e.NewRoleId).HasColumnName("new_role_id");
            entity.Property(e => e.RoleName)
                .HasMaxLength(50)
                .HasColumnName("role_name");
        });

        modelBuilder.Entity<Screen>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__screens__3213E83FD67C2672");

            entity.ToTable("screens");

            entity.HasIndex(e => e.ScreenCode, "UQ__screens__8CDE129DA49ECE5B").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(datediff(second,'1970-01-01',getutcdate()))")
                .HasColumnName("created_at");
            entity.Property(e => e.Description)
                .HasMaxLength(255)
                .HasColumnName("description");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.ScreenCode)
                .HasMaxLength(50)
                .HasColumnName("screen_code");
            entity.Property(e => e.ScreenName)
                .HasMaxLength(100)
                .HasColumnName("screen_name");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
        });

        modelBuilder.Entity<ScreenAction>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__screen_a__3213E83F776CB2E3");

            entity.ToTable("screen_actions");

            entity.HasIndex(e => new { e.ScreenId, e.ActionId }, "UQ_screen_action").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ActionId).HasColumnName("action_id");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(datediff(second,'1970-01-01',getutcdate()))")
                .HasColumnName("created_at");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.ScreenId).HasColumnName("screen_id");

            entity.HasOne(d => d.Action).WithMany(p => p.ScreenActions)
                .HasForeignKey(d => d.ActionId)
                .HasConstraintName("FK_screen_actions_action");

            entity.HasOne(d => d.Screen).WithMany(p => p.ScreenActions)
                .HasForeignKey(d => d.ScreenId)
                .HasConstraintName("FK_screen_actions_screen");
        });

        modelBuilder.Entity<StateMachineState>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__StateMac__3214EC2732F3F85B");

            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasColumnName("ID");
            entity.Property(e => e.Caption).HasMaxLength(200);
            entity.Property(e => e.Gcrecord).HasColumnName("GCRecord");
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.StartStateId).HasColumnName("StartStateID");
            entity.Property(e => e.StateMachineId).HasColumnName("StateMachine_ID");
            entity.Property(e => e.StateMachineId1).HasColumnName("StateMachineID");
        });

        modelBuilder.Entity<Subtask>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__subtasks__3213E83F6AB5384A");

            entity.ToTable("subtasks");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CompletedAt).HasColumnName("completed_at");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.IsCompleted).HasColumnName("is_completed");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");

            entity.HasOne(d => d.Task).WithMany(p => p.Subtasks)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_subtasks_task_id_tasks");
        });

        modelBuilder.Entity<SystemLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__system_l__3213E83FFBDD2D56");

            entity.ToTable("system_logs");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Details).HasColumnName("details");
            entity.Property(e => e.IpAddress)
                .HasMaxLength(50)
                .HasColumnName("ip_address");
            entity.Property(e => e.LogLevel)
                .HasMaxLength(20)
                .HasColumnName("log_level");
            entity.Property(e => e.LogType)
                .HasMaxLength(50)
                .HasColumnName("log_type");
            entity.Property(e => e.Message).HasColumnName("message");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.User).WithMany(p => p.SystemLogs)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_system_logs_user_id_users");
        });

        modelBuilder.Entity<SystemSetting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__system_s__3213E83FC7ECE2F3");

            entity.ToTable("system_settings");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.SettingGroup)
                .HasMaxLength(50)
                .HasColumnName("setting_group");
            entity.Property(e => e.SettingKey)
                .HasMaxLength(100)
                .HasColumnName("setting_key");
            entity.Property(e => e.SettingValue).HasColumnName("setting_value");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.SystemSettings)
                .HasForeignKey(d => d.CreatedBy)
                .HasConstraintName("FK_system_settings_created_by_users");
        });

        modelBuilder.Entity<Task>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__tasks__3213E83F08A84EF4");

            entity.ToTable("tasks");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ActualTime).HasColumnName("actual_time");
            entity.Property(e => e.AssigneeId).HasColumnName("assignee_id");
            entity.Property(e => e.CompletedAt).HasColumnName("completed_at");
            entity.Property(e => e.CompletionPercentage).HasColumnName("completion_percentage");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatorId).HasColumnName("creator_id");
            entity.Property(e => e.DepartmentId).HasColumnName("department_id");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.DueDate).HasColumnName("due_date");
            entity.Property(e => e.EstimatedTime).HasColumnName("estimated_time");
            entity.Property(e => e.Incoming).HasMaxLength(200);
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.Priority)
                .HasMaxLength(50)
                .HasDefaultValueSql("((0))")
                .HasColumnName("priority");
            entity.Property(e => e.StartDate).HasColumnName("start_date");
            entity.Property(e => e.Status)
                .HasMaxLength(50)
                .HasDefaultValueSql("((0))")
                .HasColumnName("status");
            entity.Property(e => e.TaskTypeId).HasColumnName("task_type_id");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");

            entity.HasOne(d => d.Assignee).WithMany(p => p.TaskAssignees)
                .HasForeignKey(d => d.AssigneeId)
                .HasConstraintName("FK_tasks_assignee");

            entity.HasOne(d => d.Creator).WithMany(p => p.TaskCreators)
                .HasForeignKey(d => d.CreatorId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_tasks_creator");

            entity.HasOne(d => d.Department).WithMany(p => p.Tasks)
                .HasForeignKey(d => d.DepartmentId)
                .HasConstraintName("FK_tasks_departments");

            entity.HasOne(d => d.TaskType).WithMany(p => p.Tasks)
                .HasForeignKey(d => d.TaskTypeId)
                .HasConstraintName("FK_tasks_task_types");

            entity.HasMany(d => d.Users).WithMany(p => p.Tasks)
                .UsingEntity<Dictionary<string, object>>(
                    "TaskAccessUser",
                    r => r.HasOne<User>().WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_task_access_users_user"),
                    l => l.HasOne<Task>().WithMany()
                        .HasForeignKey("TaskId")
                        .HasConstraintName("FK_task_access_users_task"),
                    j =>
                    {
                        j.HasKey("TaskId", "UserId");
                        j.ToTable("task_access_users");
                        j.IndexerProperty<int>("TaskId").HasColumnName("task_id");
                        j.IndexerProperty<int>("UserId").HasColumnName("user_id");
                    });
        });

        modelBuilder.Entity<TaskComment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__task_com__3213E83FB7A32E85");

            entity.ToTable("task_comments");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Content).HasColumnName("content");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.Task).WithMany(p => p.TaskComments)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_comments_tasks");

            entity.HasOne(d => d.User).WithMany(p => p.TaskComments)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_comments_users");
        });

        modelBuilder.Entity<TaskDocument>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__task_doc__3213E83FBE1B151F");

            entity.ToTable("task_documents", tb => tb.HasTrigger("tr_task_documents_updated_at"));

            entity.HasIndex(e => e.ArchiveDocumentId, "IX_task_documents_archive_document_id").HasFilter("([is_deleted]=(0))");

            entity.HasIndex(e => e.CreatedAt, "IX_task_documents_created_at")
                .IsDescending()
                .HasFilter("([is_deleted]=(0))");

            entity.HasIndex(e => e.CreatedBy, "IX_task_documents_created_by").HasFilter("([is_deleted]=(0))");

            entity.HasIndex(e => e.IsShared, "IX_task_documents_is_shared").HasFilter("([is_deleted]=(0))");

            entity.HasIndex(e => e.TaskId, "IX_task_documents_task_id").HasFilter("([is_deleted]=(0))");

            entity.HasIndex(e => new { e.TaskId, e.Type, e.IsShared }, "IX_task_documents_task_type_shared").HasFilter("([is_deleted]=(0))");

            entity.HasIndex(e => e.Type, "IX_task_documents_type").HasFilter("([is_deleted]=(0))");

            entity.HasIndex(e => new { e.TaskId, e.ArchiveDocumentId }, "UK_task_documents_task_archive").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ArchiveDocumentId).HasColumnName("archive_document_id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description)
                .HasMaxLength(500)
                .HasColumnName("description");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsShared).HasColumnName("is_shared");
            entity.Property(e => e.Permission)
                .HasMaxLength(20)
                .HasDefaultValue("read")
                .HasColumnName("permission");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.Type)
                .HasMaxLength(50)
                .HasDefaultValue("report")
                .HasColumnName("type");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.ArchiveDocument).WithMany(p => p.TaskDocuments)
                .HasForeignKey(d => d.ArchiveDocumentId)
                .HasConstraintName("FK_task_documents_archive_documents");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.TaskDocuments)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_task_documents_created_by");

            entity.HasOne(d => d.Task).WithMany(p => p.TaskDocuments)
                .HasForeignKey(d => d.TaskId)
                .HasConstraintName("FK_task_documents_tasks");
        });

        modelBuilder.Entity<TaskHistory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__task_his__3213E83F10DB968A");

            entity.ToTable("task_history");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Action)
                .HasMaxLength(50)
                .HasColumnName("action");
            entity.Property(e => e.ChangeDescription)
                .HasMaxLength(500)
                .HasColumnName("change_description");
            entity.Property(e => e.ChangeType)
                .HasMaxLength(50)
                .HasColumnName("change_type");
            entity.Property(e => e.ChangedAt).HasColumnName("changed_at");
            entity.Property(e => e.ChangedBy).HasColumnName("changed_by");
            entity.Property(e => e.Details).HasColumnName("details");
            entity.Property(e => e.NewValue).HasColumnName("new_value");
            entity.Property(e => e.OldValue).HasColumnName("old_value");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.Timestamp).HasColumnName("timestamp");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.ChangedByNavigation).WithMany(p => p.TaskHistoryChangedByNavigations)
                .HasForeignKey(d => d.ChangedBy)
                .HasConstraintName("FK_task_history_changed_by_users");

            entity.HasOne(d => d.Task).WithMany(p => p.TaskHistories)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_history_tasks");

            entity.HasOne(d => d.User).WithMany(p => p.TaskHistoryUsers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_history_users");
        });

        modelBuilder.Entity<TaskMessage>(entity =>
        {
            entity.ToTable("task_messages", tb => tb.HasComment("جدول رسائل المهام - يحتوي على جميع الرسائل والمحادثات الخاصة بالمهام"));

            entity.HasIndex(e => e.ContentType, "IX_task_messages_content_type");

            entity.HasIndex(e => e.CreatedAt, "IX_task_messages_created_at");

            entity.HasIndex(e => e.IsDeleted, "IX_task_messages_is_deleted");

            entity.HasIndex(e => e.IsPinned, "IX_task_messages_is_pinned");

            entity.HasIndex(e => e.IsRead, "IX_task_messages_is_read");

            entity.HasIndex(e => e.Priority, "IX_task_messages_priority");

            entity.HasIndex(e => e.ReplyToMessageId, "IX_task_messages_reply_to");

            entity.HasIndex(e => e.SenderId, "IX_task_messages_sender_id");

            entity.HasIndex(e => e.TaskId, "IX_task_messages_task_id");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.AttachmentIds).HasColumnName("attachment_ids");
            entity.Property(e => e.Content).HasColumnName("content");
            entity.Property(e => e.ContentType)
                .HasDefaultValue(1)
                .HasColumnName("content_type");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.FollowUpAt).HasColumnName("follow_up_at");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsEdited).HasColumnName("is_edited");
            entity.Property(e => e.IsMarkedForFollowUp).HasColumnName("is_marked_for_follow_up");
            entity.Property(e => e.IsPinned).HasColumnName("is_pinned");
            entity.Property(e => e.IsRead).HasColumnName("is_read");
            entity.Property(e => e.MarkedForFollowUpBy).HasColumnName("marked_for_follow_up_by");
            entity.Property(e => e.MentionedUserIds).HasColumnName("mentioned_user_ids");
            entity.Property(e => e.PinnedAt).HasColumnName("pinned_at");
            entity.Property(e => e.PinnedBy).HasColumnName("pinned_by");
            entity.Property(e => e.Priority).HasColumnName("priority");
            entity.Property(e => e.ReplyToMessageId).HasColumnName("reply_to_message_id");
            entity.Property(e => e.SenderId).HasColumnName("sender_id");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.MarkedForFollowUpByNavigation).WithMany(p => p.TaskMessageMarkedForFollowUpByNavigations)
                .HasForeignKey(d => d.MarkedForFollowUpBy)
                .HasConstraintName("FK_task_messages_marked_by");

            entity.HasOne(d => d.PinnedByNavigation).WithMany(p => p.TaskMessagePinnedByNavigations)
                .HasForeignKey(d => d.PinnedBy)
                .HasConstraintName("FK_task_messages_pinned_by");

            entity.HasOne(d => d.ReplyToMessage).WithMany(p => p.InverseReplyToMessage)
                .HasForeignKey(d => d.ReplyToMessageId)
                .HasConstraintName("FK_task_messages_reply_to");

            entity.HasOne(d => d.Sender).WithMany(p => p.TaskMessageSenders)
                .HasForeignKey(d => d.SenderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_task_messages_sender");

            entity.HasOne(d => d.Task).WithMany(p => p.TaskMessages)
                .HasForeignKey(d => d.TaskId)
                .HasConstraintName("FK_task_messages_tasks");
        });

        modelBuilder.Entity<TaskMessageRead>(entity =>
        {
            entity.ToTable("task_message_reads", tb => tb.HasComment("جدول قراءة رسائل المهام - يتتبع حالة قراءة كل رسالة لكل مستخدم"));

            entity.HasIndex(e => e.MessageId, "IX_task_message_reads_message_id");

            entity.HasIndex(e => e.ReadAt, "IX_task_message_reads_read_at");

            entity.HasIndex(e => e.UserId, "IX_task_message_reads_user_id");

            entity.HasIndex(e => new { e.MessageId, e.UserId }, "UQ_task_message_reads_message_user").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.DeliveredAt).HasColumnName("delivered_at");
            entity.Property(e => e.IsDelivered)
                .HasDefaultValue(true)
                .HasColumnName("is_delivered");
            entity.Property(e => e.MessageId).HasColumnName("message_id");
            entity.Property(e => e.ReadAt).HasColumnName("read_at");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.Message).WithMany(p => p.TaskMessageReads)
                .HasForeignKey(d => d.MessageId)
                .HasConstraintName("FK_task_message_reads_message");

            entity.HasOne(d => d.User).WithMany(p => p.TaskMessageReads)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_task_message_reads_user");
        });

        modelBuilder.Entity<TaskPriority>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__task_pri__3213E83FB15442DA");

            entity.ToTable("task_priorities");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.IsDefault).HasColumnName("is_default");
            entity.Property(e => e.Level).HasColumnName("level");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.TaskPriorities)
                .HasForeignKey(d => d.CreatedBy)
                .HasConstraintName("FK_task_priorities_users");
        });

        modelBuilder.Entity<TaskProgressTracker>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__task_pro__3213E83F458A5CEF");

            entity.ToTable("task_progress_trackers");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Notes).HasColumnName("notes");
            entity.Property(e => e.Progress).HasColumnName("progress");
            entity.Property(e => e.ProgressPercentage)
                .HasColumnType("decimal(5, 2)")
                .HasColumnName("progress_percentage");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UpdatedBy).HasColumnName("updated_by");

            entity.HasOne(d => d.Task).WithMany(p => p.TaskProgressTrackers)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_progress_trackers_tasks");

            entity.HasOne(d => d.UpdatedByNavigation).WithMany(p => p.TaskProgressTrackers)
                .HasForeignKey(d => d.UpdatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_progress_trackers_users");
        });

        modelBuilder.Entity<TaskStatus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__task_sta__3213E83F53C90987");

            entity.ToTable("task_statuses");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.IsDefault).HasColumnName("is_default");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.OrderIndex).HasColumnName("order_index");
        });

        modelBuilder.Entity<TaskType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__task_typ__3213E83F111FA7F0");

            entity.ToTable("task_types");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Color)
                .HasMaxLength(20)
                .HasColumnName("color");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Icon)
                .HasMaxLength(50)
                .HasColumnName("icon");
            entity.Property(e => e.IsDefault).HasColumnName("is_default");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
        });

        modelBuilder.Entity<TimeTrackingEntry>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__time_tra__3213E83F4BCA1322");

            entity.ToTable("time_tracking_entries");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Duration).HasColumnName("duration");
            entity.Property(e => e.EndTime).HasColumnName("end_time");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.StartTime).HasColumnName("start_time");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.Task).WithMany(p => p.TimeTrackingEntries)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_time_tracking_entries_task_id_tasks");

            entity.HasOne(d => d.User).WithMany(p => p.TimeTrackingEntries)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_time_tracking_entries_user_id_users");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__users__3213E83FC03D7CC9");

            entity.ToTable("users");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.DepartmentId).HasColumnName("department_id");
            entity.Property(e => e.Email)
                .HasMaxLength(100)
                .HasColumnName("email");
            entity.Property(e => e.FailedLoginAttempts)
                .HasDefaultValue(0)
                .HasColumnName("failed_login_attempts");
            entity.Property(e => e.FirstName)
                .HasMaxLength(50)
                .HasColumnName("first_name");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsOnline).HasColumnName("is_online");
            entity.Property(e => e.LastLogin).HasColumnName("last_login");
            entity.Property(e => e.LastName)
                .HasMaxLength(50)
                .HasColumnName("last_name");
            entity.Property(e => e.LastSeen).HasColumnName("last_seen");
            entity.Property(e => e.LockedUntil).HasColumnName("locked_until");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.Password)
                .HasMaxLength(255)
                .HasColumnName("password");
            entity.Property(e => e.PasswordChangedAt).HasColumnName("password_changed_at");
            entity.Property(e => e.ProfileImage)
                .HasMaxLength(255)
                .HasColumnName("profile_image");
            entity.Property(e => e.Role).HasColumnName("role");
            entity.Property(e => e.RoleId).HasColumnName("role_id");
            entity.Property(e => e.TwoFactorEnabled)
                .HasDefaultValue(false)
                .HasColumnName("two_factor_enabled");
            entity.Property(e => e.TwoFactorSecret)
                .HasMaxLength(255)
                .HasColumnName("two_factor_secret");
            entity.Property(e => e.Username)
                .HasMaxLength(50)
                .HasColumnName("username");

            entity.HasOne(d => d.Department).WithMany(p => p.Users)
                .HasForeignKey(d => d.DepartmentId)
                .HasConstraintName("FK_users_departments");

            entity.HasOne(d => d.RoleNavigation).WithMany(p => p.Users)
                .HasForeignKey(d => d.RoleId)
                .HasConstraintName("FK_users_roles");
        });

        modelBuilder.Entity<UserCustomRole>(entity =>
        {
            entity.ToTable("user_custom_roles");

            entity.HasIndex(e => e.AssignedBy, "IX_user_custom_roles_assigned_by");

            entity.HasIndex(e => e.CustomRoleId, "IX_user_custom_roles_custom_role_id");

            entity.HasIndex(e => e.UserId, "IX_user_custom_roles_user_id");

            entity.HasIndex(e => new { e.UserId, e.CustomRoleId }, "UQ_user_custom_roles_user_role").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.AssignedAt).HasColumnName("assigned_at");
            entity.Property(e => e.AssignedBy).HasColumnName("assigned_by");
            entity.Property(e => e.CustomRoleId).HasColumnName("custom_role_id");
            entity.Property(e => e.ExpiresAt).HasColumnName("expires_at");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.AssignedByNavigation).WithMany(p => p.UserCustomRoleAssignedByNavigations)
                .HasForeignKey(d => d.AssignedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_user_custom_roles_assigned_by_user");

            entity.HasOne(d => d.CustomRole).WithMany(p => p.UserCustomRoles)
                .HasForeignKey(d => d.CustomRoleId)
                .HasConstraintName("FK_user_custom_roles_custom_role");

            entity.HasOne(d => d.User).WithMany(p => p.UserCustomRoleUsers)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_user_custom_roles_user");
        });

        modelBuilder.Entity<UserPermission>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__user_per__3213E83F9769948E");

            entity.ToTable("user_permissions");

            entity.HasIndex(e => new { e.UserId, e.PermissionId }, "UQ_user_permission").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(datediff(second,'1970-01-01',getutcdate()))")
                .HasColumnName("created_at");
            entity.Property(e => e.ExpiresAt).HasColumnName("expires_at");
            entity.Property(e => e.GrantedAt).HasColumnName("granted_at");
            entity.Property(e => e.GrantedBy).HasColumnName("granted_by");
            entity.Property(e => e.IsActive)
                .HasDefaultValue(true)
                .HasColumnName("is_active");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.GrantedByNavigation).WithMany(p => p.UserPermissionGrantedByNavigations)
                .HasForeignKey(d => d.GrantedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_user_permissions_granted_by_users");

            entity.HasOne(d => d.Permission).WithMany(p => p.UserPermissions)
                .HasForeignKey(d => d.PermissionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_user_permissions_permission_id_permissions");

            entity.HasOne(d => d.User).WithMany(p => p.UserPermissionUsers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_user_permissions_user_id_users");
        });

        modelBuilder.Entity<UserPermissionsBackup>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("user_permissions_backup");

            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.ExpiresAt).HasColumnName("expires_at");
            entity.Property(e => e.GrantedAt).HasColumnName("granted_at");
            entity.Property(e => e.GrantedBy).HasColumnName("granted_by");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("id");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");
            entity.Property(e => e.UserId).HasColumnName("user_id");
        });

        modelBuilder.Entity<VwTaskDocumentsWithDetail>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_task_documents_with_details");

            entity.Property(e => e.AgeInDays).HasColumnName("age_in_days");
            entity.Property(e => e.ArchiveDescription).HasColumnName("archive_description");
            entity.Property(e => e.ArchiveDocumentId).HasColumnName("archive_document_id");
            entity.Property(e => e.CategoryId).HasColumnName("category_id");
            entity.Property(e => e.CategoryName)
                .HasMaxLength(100)
                .HasColumnName("category_name");
            entity.Property(e => e.Content)
                .HasColumnType("ntext")
                .HasColumnName("content");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.CreatedByEmail)
                .HasMaxLength(100)
                .HasColumnName("created_by_email");
            entity.Property(e => e.CreatedByFullName)
                .HasMaxLength(100)
                .HasColumnName("created_by_full_name");
            entity.Property(e => e.CreatedByUsername)
                .HasMaxLength(50)
                .HasColumnName("created_by_username");
            entity.Property(e => e.FileName)
                .HasMaxLength(255)
                .HasColumnName("file_name");
            entity.Property(e => e.FilePath)
                .HasMaxLength(255)
                .HasColumnName("file_path");
            entity.Property(e => e.FileSize).HasColumnName("file_size");
            entity.Property(e => e.FileType)
                .HasMaxLength(50)
                .HasColumnName("file_type");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
            entity.Property(e => e.IsRecent).HasColumnName("is_recent");
            entity.Property(e => e.IsShared).HasColumnName("is_shared");
            entity.Property(e => e.Metadata).HasColumnName("metadata");
            entity.Property(e => e.Permission)
                .HasMaxLength(20)
                .HasColumnName("permission");
            entity.Property(e => e.TaskDescription).HasColumnName("task_description");
            entity.Property(e => e.TaskDocumentDescription)
                .HasMaxLength(500)
                .HasColumnName("task_document_description");
            entity.Property(e => e.TaskId).HasColumnName("task_id");
            entity.Property(e => e.TaskPriority)
                .HasMaxLength(50)
                .HasColumnName("task_priority");
            entity.Property(e => e.TaskStatus)
                .HasMaxLength(50)
                .HasColumnName("task_status");
            entity.Property(e => e.TaskTitle)
                .HasMaxLength(255)
                .HasColumnName("task_title");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.Type)
                .HasMaxLength(50)
                .HasColumnName("type");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
        });

        modelBuilder.Entity<VwUserAllPermission>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_user_all_permissions");

            entity.Property(e => e.PermissionDescription).HasColumnName("permission_description");
            entity.Property(e => e.PermissionGroup)
                .HasMaxLength(50)
                .HasColumnName("permission_group");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");
            entity.Property(e => e.PermissionLevel).HasColumnName("permission_level");
            entity.Property(e => e.PermissionName)
                .HasMaxLength(100)
                .HasColumnName("permission_name");
            entity.Property(e => e.PermissionSource)
                .HasMaxLength(12)
                .IsUnicode(false)
                .HasColumnName("permission_source");
            entity.Property(e => e.UserEmail)
                .HasMaxLength(100)
                .HasColumnName("user_email");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.UserName)
                .HasMaxLength(100)
                .HasColumnName("user_name");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
