﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class TimeTrackingEntry
{
    public int Id { get; set; }

    public int TaskId { get; set; }

    public int UserId { get; set; }

    public long StartTime { get; set; }

    public long? EndTime { get; set; }

    public int? Duration { get; set; }

    public string? Description { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public virtual Task Task { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
