﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class User
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string Email { get; set; } = null!;

    public string? Username { get; set; }

    public string Password { get; set; } = null!;

    public string? ProfileImage { get; set; }

    public int? DepartmentId { get; set; }

    public int Role { get; set; }

    public bool IsActive { get; set; }

    public bool IsOnline { get; set; }

    public long CreatedAt { get; set; }

    public long? LastLogin { get; set; }

    public long? LastSeen { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public bool IsDeleted { get; set; }

    public long? PasswordChangedAt { get; set; }

    public int? FailedLoginAttempts { get; set; }

    public long? LockedUntil { get; set; }

    public bool? TwoFactorEnabled { get; set; }

    public string? TwoFactorSecret { get; set; }

    public int? RoleId { get; set; }

    public virtual ICollection<ActivityLog> ActivityLogChangedByNavigations { get; set; } = new List<ActivityLog>();

    public virtual ICollection<ActivityLog> ActivityLogUsers { get; set; } = new List<ActivityLog>();

    public virtual ICollection<ArchiveCategory> ArchiveCategories { get; set; } = new List<ArchiveCategory>();

    public virtual ICollection<ArchiveDocument> ArchiveDocumentCreatedByNavigations { get; set; } = new List<ArchiveDocument>();

    public virtual ICollection<ArchiveDocument> ArchiveDocumentUploadedByNavigations { get; set; } = new List<ArchiveDocument>();

    public virtual ICollection<ArchiveTag> ArchiveTags { get; set; } = new List<ArchiveTag>();

    public virtual ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();

    public virtual ICollection<Backup> BackupCreatedByNavigations { get; set; } = new List<Backup>();

    public virtual ICollection<Backup> BackupRestoredByNavigations { get; set; } = new List<Backup>();

    public virtual ICollection<CalendarEvent> CalendarEvents { get; set; } = new List<CalendarEvent>();

    public virtual ICollection<ChatGroup> ChatGroups { get; set; } = new List<ChatGroup>();

    public virtual ICollection<CustomRole> CustomRoleCreatedByNavigations { get; set; } = new List<CustomRole>();

    public virtual ICollection<CustomRolePermission> CustomRolePermissions { get; set; } = new List<CustomRolePermission>();

    public virtual ICollection<CustomRole> CustomRoleUpdatedByNavigations { get; set; } = new List<CustomRole>();

    public virtual ICollection<Dashboard> Dashboards { get; set; } = new List<Dashboard>();

    public virtual Department? Department { get; set; }

    public virtual ICollection<Department> Departments { get; set; } = new List<Department>();

    public virtual ICollection<GroupMember> GroupMembers { get; set; } = new List<GroupMember>();

    public virtual ICollection<LoginHistory> LoginHistories { get; set; } = new List<LoginHistory>();

    public virtual ICollection<MessageAttachment> MessageAttachments { get; set; } = new List<MessageAttachment>();

    public virtual ICollection<Message> MessageMarkedForFollowUpByNavigations { get; set; } = new List<Message>();

    public virtual ICollection<Message> MessagePinnedByNavigations { get; set; } = new List<Message>();

    public virtual ICollection<MessageReaction> MessageReactions { get; set; } = new List<MessageReaction>();

    public virtual ICollection<MessageRead> MessageReads { get; set; } = new List<MessageRead>();

    public virtual ICollection<Message> MessageReceivers { get; set; } = new List<Message>();

    public virtual ICollection<Message> MessageSenders { get; set; } = new List<Message>();

    public virtual ICollection<NotificationSetting> NotificationSettings { get; set; } = new List<NotificationSetting>();

    public virtual ICollection<Notification> Notifications { get; set; } = new List<Notification>();

    public virtual ICollection<PasswordResetToken> PasswordResetTokens { get; set; } = new List<PasswordResetToken>();

    public virtual ICollection<RefreshToken> RefreshTokens { get; set; } = new List<RefreshToken>();

    public virtual ICollection<ReportSchedule> ReportSchedules { get; set; } = new List<ReportSchedule>();

    public virtual ICollection<Report> Reports { get; set; } = new List<Report>();

    public virtual Role? RoleNavigation { get; set; }

    public virtual ICollection<SystemLog> SystemLogs { get; set; } = new List<SystemLog>();

    public virtual ICollection<SystemSetting> SystemSettings { get; set; } = new List<SystemSetting>();

    public virtual ICollection<Task> TaskAssignees { get; set; } = new List<Task>();

    public virtual ICollection<TaskComment> TaskComments { get; set; } = new List<TaskComment>();

    public virtual ICollection<Task> TaskCreators { get; set; } = new List<Task>();

    public virtual ICollection<TaskDocument> TaskDocuments { get; set; } = new List<TaskDocument>();

    public virtual ICollection<TaskHistory> TaskHistoryChangedByNavigations { get; set; } = new List<TaskHistory>();

    public virtual ICollection<TaskHistory> TaskHistoryUsers { get; set; } = new List<TaskHistory>();

    public virtual ICollection<TaskMessage> TaskMessageMarkedForFollowUpByNavigations { get; set; } = new List<TaskMessage>();

    public virtual ICollection<TaskMessage> TaskMessagePinnedByNavigations { get; set; } = new List<TaskMessage>();

    public virtual ICollection<TaskMessageRead> TaskMessageReads { get; set; } = new List<TaskMessageRead>();

    public virtual ICollection<TaskMessage> TaskMessageSenders { get; set; } = new List<TaskMessage>();

    public virtual ICollection<TaskPriority> TaskPriorities { get; set; } = new List<TaskPriority>();

    public virtual ICollection<TaskProgressTracker> TaskProgressTrackers { get; set; } = new List<TaskProgressTracker>();

    public virtual ICollection<TimeTrackingEntry> TimeTrackingEntries { get; set; } = new List<TimeTrackingEntry>();

    public virtual ICollection<UserCustomRole> UserCustomRoleAssignedByNavigations { get; set; } = new List<UserCustomRole>();

    public virtual ICollection<UserCustomRole> UserCustomRoleUsers { get; set; } = new List<UserCustomRole>();

    public virtual ICollection<UserPermission> UserPermissionGrantedByNavigations { get; set; } = new List<UserPermission>();

    public virtual ICollection<UserPermission> UserPermissionUsers { get; set; } = new List<UserPermission>();

    public virtual ICollection<Dashboard> DashboardsNavigation { get; set; } = new List<Dashboard>();

    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

    public virtual ICollection<Task> Tasks { get; set; } = new List<Task>();
}
