﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class UserCustomRole
{
    public int Id { get; set; }

    public int UserId { get; set; }

    public int CustomRoleId { get; set; }

    public bool IsActive { get; set; }

    public int AssignedBy { get; set; }

    public long AssignedAt { get; set; }

    public long? ExpiresAt { get; set; }

    public bool IsDeleted { get; set; }

    public virtual User AssignedByNavigation { get; set; } = null!;

    public virtual CustomRole CustomRole { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
