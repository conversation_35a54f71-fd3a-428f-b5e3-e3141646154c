﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class UserPermissionsBackup
{
    public int Id { get; set; }

    public int UserId { get; set; }

    public int PermissionId { get; set; }

    public int GrantedBy { get; set; }

    public long GrantedAt { get; set; }

    public bool IsActive { get; set; }

    public long? ExpiresAt { get; set; }

    public bool IsDeleted { get; set; }

    public long CreatedAt { get; set; }
}
