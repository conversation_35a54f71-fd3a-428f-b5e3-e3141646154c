﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class VwTaskDocumentsWithDetail
{
    public int TaskId { get; set; }

    public int ArchiveDocumentId { get; set; }

    public string Type { get; set; } = null!;

    public string? TaskDocumentDescription { get; set; }

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public bool IsShared { get; set; }

    public string Permission { get; set; } = null!;

    public string Title { get; set; } = null!;

    public string? ArchiveDescription { get; set; }

    public string FileName { get; set; } = null!;

    public string FilePath { get; set; } = null!;

    public string FileType { get; set; } = null!;

    public long FileSize { get; set; }

    public string? Content { get; set; }

    public string? Metadata { get; set; }

    public int? CategoryId { get; set; }

    public string? CategoryName { get; set; }

    public string? CreatedByUsername { get; set; }

    public string CreatedByFullName { get; set; } = null!;

    public string CreatedByEmail { get; set; } = null!;

    public string TaskTitle { get; set; } = null!;

    public string? TaskDescription { get; set; }

    public string TaskStatus { get; set; } = null!;

    public string TaskPriority { get; set; } = null!;

    public int? AgeInDays { get; set; }

    public int IsRecent { get; set; }
}
