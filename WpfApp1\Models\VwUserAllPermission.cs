﻿using System;
using System.Collections.Generic;

namespace WpfApp1.Models;

public partial class VwUserAllPermission
{
    public int UserId { get; set; }

    public string UserName { get; set; } = null!;

    public string UserEmail { get; set; } = null!;

    public int PermissionId { get; set; }

    public string PermissionName { get; set; } = null!;

    public string? PermissionDescription { get; set; }

    public string PermissionGroup { get; set; } = null!;

    public int PermissionLevel { get; set; }

    public string PermissionSource { get; set; } = null!;
}
