using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;
using WpfApp1.Models;

namespace WpfApp1.Services
{
    public class TaskService
    {
        private readonly TasksDbContext _context;

        public TaskService()
        {
            _context = new TasksDbContext();
            // التأكد من إنشاء قاعدة البيانات
            _context.Database.EnsureCreated();
        }

        // الحصول على جميع المهام
        public async Task<ObservableCollection<Models.Task>> GetAllTasksAsync()
        {
            var tasks = await _context.Tasks.ToListAsync();
            return new ObservableCollection<Models.Task>(tasks);
        }

        // إضافة مهمة جديدة
        public async Task<Models.Task> AddTaskAsync(Models.Task task)
        {
            task.CreatedAt = DateTimeOffset.Now.ToUnixTimeSeconds();
            _context.Tasks.Add(task);
            await _context.SaveChangesAsync();
            return task;
        }

        // تحديث مهمة موجودة
        public async Task<Models.Task> UpdateTaskAsync(Models.Task task)
        {
            _context.Tasks.Update(task);
            await _context.SaveChangesAsync();
            return task;
        }

        // حذف مهمة
        public async Task<bool> DeleteTaskAsync(int taskId)
        {
            var task = await _context.Tasks.FindAsync(taskId);
            if (task != null)
            {
                _context.Tasks.Remove(task);
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        // الحصول على مهمة بالمعرف
        public async Task<Models.Task?> GetTaskByIdAsync(int id)
        {
            return await _context.Tasks.FindAsync(id);
        }

        // الحصول على المهام حسب التاريخ
        public async Task<ObservableCollection<Models.Task>> GetTasksByDateAsync(DateTime date)
        {
            var dateUnix = new DateTimeOffset(date).ToUnixTimeSeconds();
            var tasks = await _context.Tasks
                .Where(t => t.StartDate <= dateUnix && t.DueDate >= dateUnix)
                .ToListAsync();
            return new ObservableCollection<Models.Task>(tasks);
        }

        // الحصول على المهام المكتملة
        public async Task<ObservableCollection<Models.Task>> GetCompletedTasksAsync()
        {
            var tasks = await _context.Tasks
                .Where(t => t.Status == "Completed")
                .ToListAsync();
            return new ObservableCollection<Models.Task>(tasks);
        }

        // الحصول على المهام المعلقة
        public async Task<ObservableCollection<Models.Task>> GetPendingTasksAsync()
        {
            var tasks = await _context.Tasks
                .Where(t => t.Status != "Completed")
                .ToListAsync();
            return new ObservableCollection<Models.Task>(tasks);
        }

        // تحديث حالة المهمة
        public async Task<bool> UpdateTaskStatusAsync(int taskId, bool isCompleted)
        {
            var task = await _context.Tasks.FindAsync(taskId);
            if (task != null)
            {
                task.Status = isCompleted ? "Completed" : "Pending";
                if (isCompleted)
                {
                    task.CompletedAt = DateTimeOffset.Now.ToUnixTimeSeconds();
                    task.CompletionPercentage = 100;
                }
                else
                {
                    task.CompletedAt = null;
                    task.CompletionPercentage = 0;
                }
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        // البحث في المهام
        public async Task<ObservableCollection<Models.Task>> SearchTasksAsync(string searchTerm)
        {
            var tasks = await _context.Tasks
                .Where(t => t.Title.Contains(searchTerm) ||
                           (t.Description != null && t.Description.Contains(searchTerm)))
                .ToListAsync();
            return new ObservableCollection<Models.Task>(tasks);
        }

        // تحرير الموارد
        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
