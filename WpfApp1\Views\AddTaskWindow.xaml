<Window x:Class="WpfApp1.Views.AddTaskWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp1.Views"
        mc:Ignorable="d"
        Title="إضافة مهمة جديدة" Height="500" Width="450" 
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان المهمة -->
        <Label Grid.Row="0" Content="عنوان المهمة:" FontWeight="Bold"/>
        <TextBox Grid.Row="1" Name="txtTitle" Margin="0,5,0,10" Height="25"/>

        <!-- وصف المهمة -->
        <Label Grid.Row="2" Content="وصف المهمة:" FontWeight="Bold"/>
        <TextBox Grid.Row="3" Name="txtDescription" Margin="0,5,0,10" Height="60" 
                 TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>

        <!-- تاريخ البداية -->
        <Label Grid.Row="4" Content="تاريخ ووقت البداية:" FontWeight="Bold"/>
        <DatePicker Grid.Row="5" Name="dpStartDate" Margin="0,5,0,10" Height="25"/>

        <!-- تاريخ النهاية -->
        <Label Grid.Row="6" Content="تاريخ ووقت النهاية:" FontWeight="Bold"/>
        <DatePicker Grid.Row="7" Name="dpEndDate" Margin="0,5,0,10" Height="25"/>

        <!-- خيارات إضافية -->
        <StackPanel Grid.Row="8" Orientation="Horizontal" Margin="0,10">
            <Label Content="الأولوية:" FontWeight="Bold" Width="80"/>
            <ComboBox Name="cmbPriority" Width="100" Margin="5,0">
                <ComboBoxItem Content="منخفضة" Tag="Low"/>
                <ComboBoxItem Content="متوسطة" Tag="Medium" IsSelected="True"/>
                <ComboBoxItem Content="عالية" Tag="High"/>
            </ComboBox>
            
            <Label Content="الفئة:" FontWeight="Bold" Width="60" Margin="20,0,0,0"/>
            <ComboBox Name="cmbCategory" Width="100" Margin="5,0">
                <ComboBoxItem Content="عمل" Tag="Work"/>
                <ComboBoxItem Content="شخصي" Tag="Personal"/>
                <ComboBoxItem Content="دراسة" Tag="Study"/>
                <ComboBoxItem Content="أخرى" Tag="Other"/>
            </ComboBox>
        </StackPanel>

        <!-- خيارات إضافية 2 -->
        <StackPanel Grid.Row="9" Orientation="Horizontal" Margin="0,10">
            <CheckBox Name="chkAllDay" Content="مهمة يوم كامل" Margin="0,0,20,0"/>
            <Label Content="المسؤول:" FontWeight="Bold" Width="60"/>
            <TextBox Name="txtAssignedTo" Width="150" Height="25"/>
        </StackPanel>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="10" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="btnSave" Content="حفظ" Width="80" Height="30" Margin="0,0,10,0" 
                    Click="BtnSave_Click" IsDefault="True"/>
            <Button Name="btnCancel" Content="إلغاء" Width="80" Height="30" 
                    Click="BtnCancel_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
