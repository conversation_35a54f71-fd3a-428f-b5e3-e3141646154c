using System;
using System.Windows;
using System.Windows.Controls;
using WpfApp1.Models;

namespace WpfApp1.Views
{
    /// <summary>
    /// Interaction logic for AddTaskWindow.xaml
    /// </summary>
    public partial class AddTaskWindow : Window
    {
        public Models.Task Task { get; private set; } = new Models.Task();

        public AddTaskWindow()
        {
            InitializeComponent();
            InitializeForm();
        }

        public AddTaskWindow(Models.Task existingTask) : this()
        {
            Task = existingTask;
            LoadTaskData();
            Title = "تعديل المهمة";
            btnSave.Content = "تحديث";
        }

        private void InitializeForm()
        {
            // تعيين التواريخ الافتراضية
            dpStartDate.SelectedDate = DateTime.Now;
            dpEndDate.SelectedDate = DateTime.Now.AddHours(1);

            // تعيين القيم الافتراضية
            cmbPriority.SelectedIndex = 1; // متوسطة
            cmbCategory.SelectedIndex = 0; // عمل
        }

        private void LoadTaskData()
        {
            if (Task == null) return;

            txtTitle.Text = Task.Title;
            txtDescription.Text = Task.Description ?? string.Empty;

            if (Task.StartDate.HasValue)
                dpStartDate.SelectedDate = DateTimeOffset.FromUnixTimeSeconds(Task.StartDate.Value).DateTime;
            if (Task.DueDate.HasValue)
                dpEndDate.SelectedDate = DateTimeOffset.FromUnixTimeSeconds(Task.DueDate.Value).DateTime;

            txtAssignedTo.Text = Task.AssigneeId?.ToString() ?? string.Empty;
            chkAllDay.IsChecked = false; // يمكن إضافة هذه الخاصية لاحقاً

            // تعيين الأولوية
            foreach (ComboBoxItem item in cmbPriority.Items)
            {
                if (item.Tag?.ToString() == Task.Priority)
                {
                    cmbPriority.SelectedItem = item;
                    break;
                }
            }

            // تعيين الفئة - سنستخدم TaskType بدلاً من Category
            if (Task.TaskTypeId.HasValue)
            {
                // يمكن تحسين هذا لاحقاً لعرض أسماء الأنواع
                cmbCategory.SelectedIndex = 0;
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                if (Task == null)
                {
                    Task = new Models.Task();
                }

                Task.Title = txtTitle.Text.Trim();
                Task.Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim();

                // تحويل التواريخ إلى Unix timestamp
                if (dpStartDate.SelectedDate.HasValue)
                    Task.StartDate = new DateTimeOffset(dpStartDate.SelectedDate.Value).ToUnixTimeSeconds();
                if (dpEndDate.SelectedDate.HasValue)
                    Task.DueDate = new DateTimeOffset(dpEndDate.SelectedDate.Value).ToUnixTimeSeconds();

                // تعيين المسؤول
                if (int.TryParse(txtAssignedTo.Text, out int assigneeId))
                    Task.AssigneeId = assigneeId;

                // تعيين الأولوية
                var selectedPriority = cmbPriority.SelectedItem as ComboBoxItem;
                Task.Priority = selectedPriority?.Tag?.ToString() ?? "Medium";

                // تعيين القيم الافتراضية
                if (Task.Id == 0) // مهمة جديدة
                {
                    Task.CreatorId = 1; // يجب تحديد المستخدم الحالي
                    Task.Status = "Pending";
                    Task.CompletionPercentage = 0;
                    Task.IsDeleted = false;
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            // التحقق من العنوان
            if (string.IsNullOrWhiteSpace(txtTitle.Text))
            {
                System.Windows.MessageBox.Show("يرجى إدخال عنوان المهمة", "تحقق من البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtTitle.Focus();
                return false;
            }

            // التحقق من تاريخ البداية
            if (!dpStartDate.SelectedDate.HasValue)
            {
                System.Windows.MessageBox.Show("يرجى اختيار تاريخ البداية", "تحقق من البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                dpStartDate.Focus();
                return false;
            }

            // التحقق من تاريخ النهاية
            if (!dpEndDate.SelectedDate.HasValue)
            {
                System.Windows.MessageBox.Show("يرجى اختيار تاريخ النهاية", "تحقق من البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                dpEndDate.Focus();
                return false;
            }

            // التحقق من منطقية التواريخ
            if (dpEndDate.SelectedDate <= dpStartDate.SelectedDate)
            {
                System.Windows.MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "تحقق من البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                dpEndDate.Focus();
                return false;
            }

            return true;
        }
    }
}
